import React, { Component, createRef } from 'react';
import { View, Animated, Image } from 'react-native';
import { PanGestureHandler, PinchGestureHandler, State } from 'react-native-gesture-handler';

interface ImageZoomState {
  panEnabled: boolean;
}

interface ImageZoomProps{
    imgPath:string
}

class ImageZoom extends Component<ImageZoomProps, ImageZoomState> {

  private scale = new Animated.Value(1);
  private translateX = new Animated.Value(0);
  private translateY = new Animated.Value(0);
  private pinchRef = createRef<PinchGestureHandler>();
  private panRef = createRef<PanGestureHandler>();

  constructor(props) {
    super(props);
    this.state = {
      panEnabled: false,
    };
  }

  onPinchEvent = Animated.event(
    [{ nativeEvent: { scale: this.scale } }],
    { useNativeDriver: true }
  );

  onPanEvent = Animated.event(
    [{
      nativeEvent: {
        translationX: this.translateX,
        translationY: this.translateY,
      },
    }],
    { useNativeDriver: true }
  );

  handlePinchStateChange = ({ nativeEvent }: any) => {
    if (nativeEvent.state === State.ACTIVE) {
      this.setState({ panEnabled: true });
    }

    if (nativeEvent.state === State.END) {
      const nScale = nativeEvent.scale;
      if (nScale < 1) {
        Animated.spring(this.scale, {
          toValue: 1,
          useNativeDriver: true,
        }).start();
        Animated.spring(this.translateX, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
        Animated.spring(this.translateY, {
          toValue: 0,
          useNativeDriver: true,
        }).start();

        this.setState({ panEnabled: false });
      }
    }
  };

  render() {
    const { imgPath } = this.props;
    return (
      <View style={{ flex: 1 }}>
        <PanGestureHandler
          onGestureEvent={this.onPanEvent}
          ref={this.panRef}
          simultaneousHandlers={this.pinchRef}
          enabled={this.state.panEnabled}
          failOffsetX={[-1000, 1000]}
          shouldCancelWhenOutside
        >
          <Animated.View>
            <PinchGestureHandler
              ref={this.pinchRef}
              onGestureEvent={this.onPinchEvent}
              simultaneousHandlers={this.panRef}
              onHandlerStateChange={this.handlePinchStateChange}
            >
              <Animated.Image
                source={{ uri: imgPath }}
                style={{
                  width: '100%',
                  height: '100%',
                  transform: [
                    { scale: this.scale },
                    { translateX: this.translateX },
                    { translateY: this.translateY },
                  ],
                }}
                resizeMode="contain"
              />
            </PinchGestureHandler>
          </Animated.View>
        </PanGestureHandler>
      </View>
    );
  }
}

export default ImageZoom;
