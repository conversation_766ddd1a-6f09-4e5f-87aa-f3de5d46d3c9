import React, { Component } from 'react';
import { View, Pressable, StyleSheet } from 'react-native';
import { scale } from 'react-native-size-matters';
import { appColors } from '../utils/appColors';

interface CheckBoxProps {
    onPress?: () => void;
    isChecked: boolean;
}

class Checkbox extends Component<CheckBoxProps> {
    handlePress = () => {
        const { onPress } = this.props;
        if (onPress) {
            onPress();
        }
    };

    render() {
        const { isChecked } = this.props;

        return (
            <Pressable
                onPress={this.handlePress}
                style={[
                    styles.base,
                    isChecked ? styles.checked : styles.unChecked,
                ]}
            />
        );
    }
}

const styles = StyleSheet.create({
    base: {
        borderRadius: scale(15),
        borderColor: appColors.white,
        height: scale(25),
        width: scale(25),
    },
    checked: {
        borderWidth: scale(5),
        backgroundColor: appColors.primary,
        borderColor: appColors.white,
    },
    unChecked: {
        backgroundColor: appColors.gray,
    },
});

export default Checkbox;
