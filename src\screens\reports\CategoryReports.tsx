import React, {Component} from 'react';
import {
    StyleSheet,
    Text,
    View,
    TouchableOpacity,
    Image,
    SectionList,
    SectionListData,
    SectionListRenderItemInfo
} from 'react-native';
import {scale, verticalScale, moderateScale} from 'react-native-size-matters';

type Item = {
    key: number;
    name: string;
    image: string;
};

type Section = {
    title: string;
    data: Item[];
};

type State = {
    results: Section[];
};

export default class CategoryReports extends Component < {},
State > {
    constructor(props : {}) {
        super(props);

        const data : Section[] = [
            {
                title: 'A',
                data: [
                    {
                        key: 1,
                        name: 'User 1',
                        image: 'https://bootdey.com/img/Content/avatar/avatar6.png'
                    }, {
                        key: 2,
                        name: 'User 2',
                        image: 'https://bootdey.com/img/Content/avatar/avatar1.png'
                    }, {
                        key: 3,
                        name: 'User 3',
                        image: 'https://bootdey.com/img/Content/avatar/avatar7.png'
                    }
                ]
            }, {
                title: 'B',
                data: [
                    {
                        key: 1,
                        name: 'User 1',
                        image: 'https://bootdey.com/img/Content/avatar/avatar3.png'
                    }, {
                        key: 2,
                        name: 'User 2',
                        image: 'https://bootdey.com/img/Content/avatar/avatar4.png'
                    }
                ]
            }, {
                title: 'C',
                data: [
                    {
                        key: 1,
                        name: 'User 1',
                        image: 'https://bootdey.com/img/Content/avatar/avatar5.png'
                    }
                ]
            }, {
                title: 'D',
                data: [
                    {
                        key: 1,
                        name: 'User 1',
                        image: 'https://bootdey.com/img/Content/avatar/avatar2.png'
                    }
                ]
            }
        ];

        this.state = {
            results: data
        };
    }

    renderSectionHeader = ({section} : {
        section: SectionListData < Item,
        Section >
    }) => {
        return (
            <View style={styles.titleContainer}>
                <Text style={styles.title}>{section.title}</Text>
            </View>
        );
    };

    renderItem = ({item} : SectionListRenderItemInfo < Item, Section >) => {
        return (
            <View style={styles.container}>
                <TouchableOpacity onPress={() => {}}>
                    <Image
                        style={styles.image}
                        source={{
                        uri: item.image
                    }}/>
                </TouchableOpacity>
                <View style={styles.content}>
                    <View style={styles.contentHeader}>
                        <Text style={styles.name}>{item.name}</Text>
                    </View>
                </View>
            </View>
        );
    };

    render() {
        return (
            <View style={styles.root}>
                <SectionList
                    sections={this.state.results}
                    keyExtractor={(item) => item.key.toString()}
                    renderSectionHeader={this.renderSectionHeader}
                    renderItem={this.renderItem}/>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    root: {
        padding: moderateScale(10)
    },
    titleContainer: {
        shadowColor: '#00000021',
        shadowOffset: {
            width: scale(2),
            height: 0
        },
        shadowOpacity: 0.5,
        shadowRadius: moderateScale(4),
        marginVertical: verticalScale(8),
        backgroundColor: '#DCDCDC',
        padding: moderateScale(10)
    },
    title: {
        fontSize: moderateScale(25),
        color: '#000000'
    },
    container: {
        paddingVertical: verticalScale(12),
        flexDirection: 'row',
        alignItems: 'flex-start'
    },
    content: {
        marginLeft: scale(16),
        flex: 1
    },
    contentHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: verticalScale(6)
    },
    separator: {
        height: verticalScale(1),
        backgroundColor: '#CCCCCC'
    },
    image: {
        width: scale(45),
        height: scale(45),
        borderRadius: scale(20),
        marginLeft: scale(20)
    },
    time: {
        fontSize: moderateScale(11),
        color: '#808080'
    },
    name: {
        fontSize: moderateScale(16),
        fontWeight: 'bold'
    }
});
