import React, { Component } from 'react';
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  FlatList,
  ListRenderItemInfo,
} from 'react-native';
import { ScaledSheet, moderateScale } from 'react-native-size-matters';
import { renderBold, renderRegular } from '../../services/FontProvider';
import {withTranslation, WithTranslation} from 'react-i18next';
interface StatItem {
  category: string;
  value: number;
}

interface State {
  data: StatItem[];
}

interface PersonalDetailsProps extends WithTranslation{}

class PersonalDetails extends Component<PersonalDetailsProps, State> {
  constructor(props) {
    super(props);
    this.state = {
      data: [
        { category: 'Followers', value: 1000 },
        { category: 'Likes', value: 500 },
        { category: 'One category', value: 3300 },
      ],
    };
  }

  renderStatItem = ({ item }: ListRenderItemInfo<StatItem>) => (
    <View style={styles.statItem}>
      <Text style={styles.statValue}>{item.value}</Text>
      <Text style={styles.statsCategory}>{item.category}</Text>
    </View>
  );

  render() {
    return (
      <View style={styles.container}>
        <View style={styles.userCard}>
          <View>
            <Image
              source={{ uri: 'https://www.bootdey.com/img/Content/avatar/avatar1.png' }}
              style={styles.userPhoto}
            />
          </View>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>John Doe</Text>
          </View>
        </View>

        <View style={styles.statsCard}>
          <Text style={styles.statsTitle}>Weekly Stats</Text>
          <FlatList
            data={this.state.data}
            renderItem={this.renderStatItem}
            keyExtractor={(_, index) => index.toString()}
            numColumns={2}
          />
        </View>

        <TouchableOpacity style={styles.addButton}>
          <Text style={styles.addButtonText}>+</Text>
        </TouchableOpacity>
      </View>
    );
  }
}

const styles = ScaledSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: '20@s',
    paddingVertical: '10@vs',
  },
  userPhoto: {
    width: '50@s',
    height: '50@s',
    borderRadius: '25@s',
  },
  userInfo: {
    flex: 1,
    marginLeft: '10@s',
  },
  userName: {
    fontWeight: 'bold',
    fontSize: '18@ms',
    marginBottom: '5@vs',
  },
  userFollowers: {
    color: '#999',
  },
  editButton: {
    padding: '10@s',
    borderRadius: '5@s',
    backgroundColor: '#008B8B',
  },
  editButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  statsCard: {
    marginHorizontal: '20@s',
    marginVertical: '10@vs',
    padding: '20@s',
    borderRadius: '10@s',
  },
  statsTitle: {
    fontWeight: 'bold',
    fontSize: '18@ms',
    marginBottom: '10@vs',
  },
  statItem: {
    marginTop: '20@vs',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: '10@s',
    marginVertical: '5@vs',
    borderRadius: '5@s',
    backgroundColor: '#fff',
    padding: '10@s',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: moderateScale(2),
    },
    shadowOpacity: 0.25,
    shadowRadius: moderateScale(3.84),
    elevation: 5,
  },
  statValue: {
    fontSize: '18@ms',
    fontWeight: 'bold',
    marginBottom: '5@vs',
  },
  statsCategory: {
    color: '#999',
  },
  addButton: {
    position: 'absolute',
    bottom: '20@vs',
    right: '20@s',
    width: '60@s',
    height: '60@s',
    borderRadius: '30@s',
    backgroundColor: '#6495ED',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: moderateScale(2),
    },
    shadowOpacity: 0.25,
    shadowRadius: moderateScale(3.84),
    elevation: 5,
  },
  addButtonText: {
    fontSize: '30@ms',
    fontWeight: 'bold',
    color: '#fff',
  },
});

export default withTranslation()(PersonalDetails);
