import React, { Component } from 'react';
import { View, StyleSheet, Button } from 'react-native';
import { scale } from 'react-native-size-matters';
import { appColors } from '../utils/appColors';
import Label from './Label';

interface Props {
  buttonLabel: string;
  priceLabel?: string;
  price?: string;
  onPress?: () => void;
}

class BottomButtons extends Component<Props> {
  render() {
    const { buttonLabel, priceLabel, price, onPress } = this.props;

    return (
      <View style={styles.container}>
        <View style={styles.priceContainer}>
          { priceLabel && (
            <Label
              text={priceLabel}
              style={styles.priceLabel}
            />
          )
          }
          {price && (
            <Label
              text={price}
              style={styles.priceText}
            />
          )}
        </View>
        <Button onPress={onPress} title={buttonLabel} />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    bottom: scale(15),
    paddingHorizontal: scale(20),
    backgroundColor: appColors.white,
  },
  priceContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: scale(14),
    opacity: 0.4,
    letterSpacing: scale(2),
  },
  priceText: {
    fontSize: scale(18),
    fontWeight: '800',
    color: appColors.primary,
    marginTop: scale(7),
  },
});

export default BottomButtons;
