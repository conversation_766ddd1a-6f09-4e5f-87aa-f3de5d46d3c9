import React, {Component} from 'react';
import { WithTranslation, withTranslation } from 'react-i18next';
import {
    View,
    Text,
    TextInput,
    FlatList,
    Image,
    TouchableOpacity,
    StyleSheet,
    ListRenderItemInfo,
    Pressable
} from 'react-native';
import {moderateScale, scale, verticalScale} from 'react-native-size-matters';
import { renderBold, renderRegular } from '../../services/FontProvider';
import { AntDesign, FontAwesome, FontAwesome5, Fontisto, Ionicons, MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';


interface Appointment {
    id : number;
    icon: React.ReactElement,
    description : string;
    page:string;
    backgroundColor : string;
    titleColor : string;
}

interface State {
    searchQuery : string;
    appointments : Appointment[];
}
interface ReportMainProps extends WithTranslation{}

class ReportMain extends Component < ReportMainProps, State > {
    constructor(props) {
        super(props);
        this.state = {
            searchQuery: '',
            appointments: [
                {
                    id: 1,
                    icon:<AntDesign name="calendar" size={scale(40)} color="black" />,
                    description: 'reportMain.daily',
                    page:"Daily",
                    backgroundColor: '#ffdcb2',
                    titleColor: '#ff8c00'
                }, {
                    id: 2,
                    icon:<FontAwesome name="money" size={scale(40)} color="black" />,
                    description: 'reportMain.income',
                    page:"MarginSales",
                    backgroundColor: '#bfdfdf',
                    titleColor: '#008080'
                }, {
                    id: 3,
                    icon:<FontAwesome5 name="hotjar" size={scale(40)} color="black" />,
                    description: 'reportMain.topSales',
                    page:"TopSales",
                    backgroundColor: '#e2caf8',
                    titleColor: '#8a2be2'
                }, {
                    id: 4,
                    icon:<MaterialIcons name="severe-cold" size={scale(40)} color="black" />,
                    description: 'reportMain.nullSales',
                    page:"NullSales",
                    backgroundColor: '#d8e4fa',
                    titleColor: '#6495ed'
                }, {
                    id: 5,
                    icon:<Fontisto name="arrow-return-left" size={scale(40)} color="black" />,
                    description: 'reportMain.returns',
                    page:"Returns",
                    backgroundColor: '#ffccff',
                    titleColor: '#ff00ff'
                }, {
                    id: 6,
                    icon:<MaterialIcons name="category" size={scale(40)} color="black" />,
                    description: 'reportMain.categorySales',
                    page:"CategoryReports",
                    backgroundColor: '#c7e3ff',
                    titleColor: '#1e90ff'
                },{
                    id: 7,
                    icon:<Ionicons name="git-compare-outline" size={scale(40)} color="black" />,
                    description: 'reportMain.compareSales',
                    backgroundColor: '#c7e3ff',
                    page:"CompareSales",
                    titleColor: '#1e90ff'
                },{
                    id: 8,
                    icon:<MaterialCommunityIcons name="division" size={scale(40)} color="black" />,
                    description: 'reportMain.averageInvoice',
                    page:"AverageInvoice",
                    backgroundColor: '#c7e3ff',
                    titleColor: '#1e90ff'
                }
            ]
        };
    }


    renderAppointmentCard = ({item} : ListRenderItemInfo < Appointment >) => {
      const {t} = this.props;
      return(
        <TouchableOpacity
            style={[
            styles.card, {
                backgroundColor: item.backgroundColor,
                borderTopWidth: moderateScale(4),
                borderTopColor: item.titleColor
            }
        ]}
            onPress={() => this.props.navigation.navigate(item.page)}
        >
            <View style={styles.iconContainer}>
                {item.icon}
            </View>
            <View style={styles.cardDates}>
                <Text style={styles.cardDate}>{t(item.description)}</Text>
            </View>
        </TouchableOpacity>
        )
    };

    render() {
        const { appointments } = this.state;
        return (
            <View style={styles.container}>
                <FlatList
                    contentContainerStyle={styles.listContainer}
                    data={appointments}
                    renderItem={this.renderAppointmentCard}
                    keyExtractor={(item) => item.id.toString()}
                    numColumns={2}/>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: moderateScale(10),
    },
    listContainer: {
        paddingHorizontal: moderateScale(5)
    },
    title: {
        fontSize: moderateScale(20),
        fontWeight: 'bold',
        marginBottom: verticalScale(10)
    },
    searchInput: {
        height: verticalScale(40),
        borderWidth: moderateScale(2),
        borderRadius: moderateScale(5),
        borderColor: '#A9A9A9',
        marginBottom: verticalScale(10),
        paddingHorizontal: moderateScale(10)
    },
    card: {
        flex: 1,
        marginBottom: verticalScale(20),
        padding: moderateScale(10),
        borderRadius: moderateScale(8),
        marginHorizontal: moderateScale(10),

        shadowColor: '#000',
        shadowOpacity: 0.2,
        shadowOffset: {
            width: 0,
            height: verticalScale(2)
        },
        shadowRadius: moderateScale(4)
    },
    iconContainer:{
        alignItems:'center',
        justifyContent:'center',
        paddingVertical:verticalScale(5)
    },
    cardDates: {
        flexDirection: 'row',
        paddingVertical: verticalScale(5),
        justifyContent:"center"
    },
    cardDate: {
        color: '#888',
        fontSize: moderateScale(16),
        fontFamily: renderRegular(),
        textAlign:'center',
    },
});

export default withTranslation()(ReportMain);