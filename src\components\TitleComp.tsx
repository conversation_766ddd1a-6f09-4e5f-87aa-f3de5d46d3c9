import React, { Component, ReactNode } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { scale } from 'react-native-size-matters';
import Label from './Label';
import { appColors } from '../utils/appColors';

interface TitleCompProps {
  heading: string;
  rightLabel?: string;
  renderRight?: () => ReactNode;
  subLabel?: string;
}

class TitleComp extends Component<TitleCompProps> {
  render() {
    const { heading, rightLabel, renderRight, subLabel } = this.props;

    return (
      <View style={styles.container}>
        <View>
          <Label text={heading} style={styles.heading} />
          {subLabel && <Label text={subLabel} style={styles.subLabel} />}
        </View>
        {!renderRight && rightLabel && <Label text={rightLabel} style={styles.rightLabel} />}
        {renderRight && renderRight()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  heading: {
    fontSize: scale(20),
    paddingBottom:scale(5),
    fontWeight: 'bold',
    color:appColors.primary
  },
  subLabel: {
    fontSize: scale(12),
    opacity: 0.5,
    marginTop: scale(10),
  },
  rightLabel: {
    fontSize: scale(14),
  },
});

export default TitleComp;
