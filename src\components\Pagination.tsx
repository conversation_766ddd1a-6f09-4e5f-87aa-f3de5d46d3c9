import React, { Component } from 'react';
import { View, Text, Button, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { PaginationResponse } from '../models/Page';
import { appColors } from '../utils/appColors';

interface PaginationProps {
  data: PaginationResponse;
  onPageChange: (pageNumber: number) => void;
}


interface PaginationState {
  currentPage: number;
}

export default class Pagination extends Component<PaginationProps, PaginationState> {
  constructor(props: PaginationProps) {
    super(props);
    this.state = {
      currentPage: props.data.number, // Initialize with the current page from props
    };
  }

  handlePageChange = (pageNumber: number) => {
    const { onPageChange } = this.props;
    this.setState({ currentPage: pageNumber });
    onPageChange(pageNumber);
  };

  generatePageNumbers = (): number[] => {
    const { data } = this.props;
    console.log(data)
    const { currentPage } = this.state;
    const totalPages = data.totalPages;

    const startPage = Math.max(0, currentPage - 4); // Show 4 pages before the current page
    const endPage = Math.min(totalPages - 1, currentPage + 5); // Show 5 pages after the current page

    const pages: number[] = [];
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  };

  render() {
    const { data } = this.props;
    const { currentPage } = this.state;

    const pageNumbers = this.generatePageNumbers();

    return (
      <View style={styles.container}>
        {/* <Text style={styles.text}>Page {currentPage + 1} of {data.totalPages}</Text>
        <Text style={styles.text}>Total Elements: {data.totalElements}</Text> */}

        <ScrollView horizontal contentContainerStyle={styles.paginationContainer}>
          <Button
            title="Previous"
            onPress={() => this.handlePageChange(currentPage - 1)}
            disabled={data.first}
          />
          {pageNumbers.map((page) => (
            <TouchableOpacity
              key={page}
              style={[
                styles.pageButton,
                page === currentPage ? styles.activePage : null,
              ]}
              onPress={() => this.handlePageChange(page)}
            >
              <Text style={styles.pageText}>{page + 1}</Text>
            </TouchableOpacity>
          ))}
          <Button
            title="Next"
            onPress={() => this.handlePageChange(currentPage + 1)}
            disabled={data.last}
          />
        </ScrollView>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    alignItems: 'center',
  },
  text: {
    fontSize: 16,
    marginVertical: 10,
  },
  paginationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 20,
  },
  pageButton: {
    padding: 10,
    marginHorizontal: 5,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
  },
  activePage: {
    backgroundColor: appColors.primary,
    color: appColors.white
  },
  pageText: {
    color:  appColors.black
  },
});

