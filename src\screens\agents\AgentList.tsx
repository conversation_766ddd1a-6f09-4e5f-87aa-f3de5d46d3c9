import { MaterialCommunityIcons } from '@expo/vector-icons';
import React, { Component } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Image, 
  FlatList, 
  ListRenderItemInfo,
} from 'react-native';
import { appColors } from '../../utils/appColors';
import { scale } from 'react-native-size-matters';

interface Friend {
  id: string;
  name: string;
  address: string;
  phone: string;
  iconName: string;
}

interface State {}

export default class AgentList extends Component<{}, State> {
  private friends: Friend[] = [
    {
      id: '1',
      name: 'Apteka 1',
      address: 'Netfciler',
      phone: "**********",   
      iconName: 'hospital-building',
    },
    {
      id: '2',
      name: '<PERSON>',
      address: '<PERSON><PERSON>',
      phone: "**********",   
      iconName: 'office-building',
    },
    {
      id: '3',
      name: '<PERSON>',
      address: 'Canada',
      phone: "********",   
      iconName: 'shopping',
    },
    {
      id: '4',
      name: '<PERSON>',
      address: 'Canada',
      phone: "*********",   
      iconName: 'bank',
    },
    {
      id: '5',
      name: '<PERSON>',
      address: 'Canada', 
      phone: "**********",   
      iconName: 'human-male',
    },
  ];

  private renderFriend = ({ item }: ListRenderItemInfo<Friend>) => {
    return (
      <View style={styles.card}>
        <MaterialCommunityIcons name={item.iconName} size={scale(50)} color={appColors.black} />
        <View style={styles.info}>
          <Text style={styles.name}>{item.name}</Text>
          <Text style={styles.details}>
            {item.address}
          </Text>
          <Text style={styles.sports}>{item.phone}</Text>
        </View>
      </View>
    );
  };

  render() {
    return (
      <FlatList
        data={this.friends}
        renderItem={this.renderFriend}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.container}
        numColumns={1}
        style={{flex: 1}}
      />
    );
  }
}


const styles = StyleSheet.create({
  container: {
    padding: 10,

  },
  card: {
    flexDirection: 'row',
    backgroundColor: '#fbfbfb',
    borderWidth: 4,
    borderColor: '#DCDCDC',
    padding: 10,
    borderRadius: 10,
    marginBottom: 10,
    elevation: 2,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  info: {
    marginLeft: 10,
    flex: 1,
  },
  name: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  details: {
    fontSize: 14,
    color: '#888',
    marginVertical: 5,
  },
  ratingContainer: {
    flexDirection: 'row',
  },
  star: {
    fontSize: 16,
    color: '#FFD700',
  },
  sports: {
    fontSize: 14,
    color: '#888',
    marginVertical: 5,
  },
  button: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#2ECC71',
    paddingVertical: 5,
    paddingHorizontal: 20,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 10,
  },
  buttonText: {
    color: '#2ECC71',
    fontSize: 16,
  },
});
