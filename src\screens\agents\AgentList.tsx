import { MaterialCommunityIcons } from '@expo/vector-icons';
import React, { Component } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  ListRenderItemInfo,
  Pressable,
} from 'react-native';
import { appColors } from '../../utils/appColors';
import { scale, verticalScale, moderateScale } from 'react-native-size-matters';
import {withTranslation, WithTranslation} from 'react-i18next';


interface Friend {
  id: string;
  name: string;
  address: string;
  phone: string;
  iconName: string;
}

interface AgentListState {}

interface AgentListProps extends WithTranslation {}

class AgentList extends Component<AgentListProps, AgentListState> {
  private friends: Friend[] = [
    {
      id: '1',
      name: 'Apteka 1',
      address: 'Netfciler',
      phone: "**********",   
      iconName: 'hospital-building',
    },
    {
      id: '2',
      name: '<PERSON>',
      address: '<PERSON><PERSON><PERSON><PERSON>',
      phone: "**********",   
      iconName: 'office-building',
    },
    {
      id: '3',
      name: '<PERSON>',
      address: 'Canada',
      phone: "********",   
      iconName: 'shopping',
    },
    {
      id: '4',
      name: '<PERSON>',
      address: 'Canada',
      phone: "*********",   
      iconName: 'bank',
    },
    {
      id: '5',
      name: '<PERSON> Parker',
      address: 'Canada', 
      phone: "**********",   
      iconName: 'human-male',
    },
    {
      id: '6',
      name: 'John Parker',
      address: 'Canada', 
      phone: "**********",   
      iconName: 'human-male',
    },
  ];

  private renderFriend = ({ item }: ListRenderItemInfo<Friend>) => {
    return (
      <Pressable style={styles.card} onPress={() => console.log(item)}>
        <MaterialCommunityIcons name={item.iconName} size={scale(50)} color={appColors.black} />
        <View style={styles.info}>
          <Text style={styles.name}>{item.name}</Text>
          <Text style={styles.details}>
            {item.address}
          </Text>
          <Text style={styles.sports}>{item.phone}</Text>
        </View>
      </Pressable>
    );
  };

  render() {
    return (
      <FlatList
        data={this.friends}
        renderItem={this.renderFriend}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.container}
        numColumns={1}
        style={{flex: 1}}
      />
    );
  }
}

export default withTranslation()(AgentList);

const styles = StyleSheet.create({
  container: {
    padding: scale(10),
  },
  card: {
    flexDirection: 'row',
    backgroundColor: '#fbfbfb',
    borderWidth: scale(4),
    borderColor: '#DCDCDC',
    padding: scale(10),
    borderRadius: scale(10),
    marginBottom: verticalScale(10),
    elevation: scale(2),
  },
  image: {
    width: scale(80),
    height: scale(80),
    borderRadius: scale(40),
  },
  info: {
    marginLeft: scale(10),
    flex: 1,
  },
  name: {
    fontSize: moderateScale(18),
    fontWeight: 'bold',
  },
  details: {
    fontSize: moderateScale(14),
    color: '#888',
    marginVertical: verticalScale(5),
  },
  ratingContainer: {
    flexDirection: 'row',
  },
  star: {
    fontSize: moderateScale(16),
    color: '#FFD700',
  },
  sports: {
    fontSize: moderateScale(14),
    color: '#888',
    marginVertical: verticalScale(5),
  },
  button: {
    backgroundColor: '#fff',
    borderWidth: scale(1),
    borderColor: '#2ECC71',
    paddingVertical: verticalScale(5),
    paddingHorizontal: scale(20),
    borderRadius: scale(5),
    alignItems: 'center',
    marginTop: verticalScale(10),
  },
  buttonText: {
    color: '#2ECC71',
    fontSize: moderateScale(16),
  },
});
