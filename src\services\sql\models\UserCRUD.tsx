import * as SQLite from 'expo-sqlite';

export default class UserCRUD {
    private db: SQLite.SQLiteDatabase | null = null;

    constructor() {
        this.initDB();
    }

    async initDB() : Promise < void > {
        this.db = await SQLite.openDatabaseAsync('Reports.db');
    }



    // Create: Insert a new user
    createUser(name: string, email: string): Promise<void> {
        return new Promise((resolve, reject) => {
        this.db.transaction(tx => {
            tx.executeSql(
            `INSERT INTO users (name, email) VALUES (?, ?);`,
            [name, email],
            () => resolve(),
            (_, error) => {
                reject(error);
                return false;
            }
            );
        });
        });
    }

    // Read: Get all users
    getUsers(): Promise<{id: number; name: string; email: string}[]> {
        return new Promise((resolve, reject) => {
        this.db.transaction(tx => {
            tx.executeSql(
            `SELECT * FROM users;`,
            [],
            (_, results) => {
                const users = [];
                for (let i = 0; i < results.rows.length; i++) {
                users.push(results.rows.item(i));
                }
                resolve(users);
            },
            (_, error) => {
                reject(error);
                return false;
            }
            );
        });
        });
    }

    // Update: Update user by id
    updateUser(id: number, name: string, email: string): Promise<void> {
        return new Promise((resolve, reject) => {
        this.db.transaction(tx => {
            tx.executeSql(
            `UPDATE users SET name = ?, email = ? WHERE id = ?;`,
            [name, email, id],
            () => resolve(),
            (_, error) => {
                reject(error);
                return false;
            }
            );
        });
        });
    }

    // Delete: Delete user by id
    deleteUser(id: number): Promise<void> {
        return new Promise((resolve, reject) => {
        this.db.transaction(tx => {
            tx.executeSql(
            `DELETE FROM users WHERE id = ?;`,
            [id],
            () => resolve(),
            (_, error) => {
                reject(error);
                return false;
            }
            );
        });
        });
    }
}