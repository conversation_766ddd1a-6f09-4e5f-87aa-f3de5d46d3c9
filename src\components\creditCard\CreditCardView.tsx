import React, { Component } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
} from "react-native";
import { scale } from "react-native-size-matters";
import { appColors } from "../../utils/appColors";
import { PaymentCard } from '../../models/PaymentCard';
import FlipCard from 'react-native-flip-card';
import { LinearGradient } from "expo-linear-gradient";
import Entypo from "@expo/vector-icons/build/Entypo";
import { AntDesign } from "@expo/vector-icons";

interface PaymentCardProps {
    cardInfo: PaymentCard;
    cardURL:string;
    flipped: boolean;
    isEyeClosed?:boolean;
    isDeletable?:boolean;
    isEditable?:boolean;
    delete?:(id) => void;
    edit?:(id) => void;
}

interface PaymentCardState {
    isEyeClosedWithInState:boolean;
    color1:string;
    color2:string;
}

function generateRandomColorCode(): string {
    const randomColor = Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0');
    console.log(randomColor)
    return `#${randomColor}`;
}


class CreditCardView extends Component<PaymentCardProps, PaymentCardState> {

    constructor(props: PaymentCardProps) {
    super(props);
    this.state = {
        isEyeClosedWithInState: true,
        color1: generateRandomColorCode(),
        color2: generateRandomColorCode(),
    };
  }

  render() {
    const { cardInfo, cardURL, flipped, isEyeClosed, isDeletable, isEditable } = this.props;
    let { isEyeClosedWithInState, color1, color2 } = this.state;
    return (
        <FlipCard
            flipHorizontal
            flipVertical={false}
            friction={10}
            perspective={2000}
            clickable={false}
            flip={flipped}
            key={color1}
        >
            <LinearGradient
                style={[creditCardStyles.cardFace]}
                //colors={['#707eff', '#505bbf']} // Gradient colors
                colors={[color1, color2]}
                start={{ x: -1, y: 1 }}          // Gradient start point
                end={{ x: 1, y: 0 }}  
            >
                <View style={creditCardStyles.logoAndIcons}>
                  <Image
                    source={{
                      uri: cardURL,
                    }}
                    style={creditCardStyles.logo}
                  />
                <View style={creditCardStyles.iconAndMain}>
                {
                    cardInfo.main === true
                    ?                       
                    <View style={creditCardStyles.mainLabelContainer}>
                        <Text style={creditCardStyles.mainLabel}>MAIN</Text>
                    </View>
                    :
                    null
                }

                {
                    isDeletable === true && cardInfo.main !== true
                    ?                       
                    <TouchableOpacity style={{ alignSelf: "center" }} onPress={() => {this.props.delete(cardInfo.id)}}>
                        <AntDesign name="delete" size={scale(25)} color={appColors.white} />
                    </TouchableOpacity>
                    :
                    null
                }

                {
                    isEditable === true
                    ?                       
                    <TouchableOpacity style={{ alignSelf: "center" }} onPress={() => {this.props.edit(cardInfo.id)}}>
                        <AntDesign name="edit" size={scale(25)} color={appColors.white} />
                    </TouchableOpacity>
                    :
                    null
                }
                {
                    isEyeClosed === true && isEyeClosedWithInState===true
                    ?
                    <TouchableOpacity style={{ alignSelf: "center" }} onPress={() => {this.setState({isEyeClosedWithInState : false})}}>
                        <Entypo name="eye-with-line" size={scale(25)} color={appColors.white} />
                    </TouchableOpacity>
                    :
                    isEyeClosed === true && isEyeClosedWithInState===false
                    ?
                    <TouchableOpacity style={{ alignSelf: "center" }} onPress={() => {this.setState({isEyeClosedWithInState : true})}}>
                        <Entypo name="eye" size={scale(25)} color={appColors.white} />
                    </TouchableOpacity>
                    :
                    null
                }
                </View>
                </View>
                  {
                      isEyeClosed==undefined || isEyeClosed==null
                      ?
                      cardInfo === null || cardInfo === undefined || cardInfo?.cardNumber === '' || cardInfo?.cardNumber === undefined
                      ?
                            (<Text style={creditCardStyles.cardNumber}>•••• •••• •••• ••••</Text>)
                      :
                            (<Text style={creditCardStyles.cardNumber}>{cardInfo.cardNumber}</Text>)
                      :
                      isEyeClosed === true && isEyeClosedWithInState===true
                      ?
                            (<Text style={creditCardStyles.cardNumber}>•••• •••• •••• ••••</Text>)
                      :
                      isEyeClosed === true && isEyeClosedWithInState===false
                      ?
                            (<Text style={creditCardStyles.cardNumber}>{cardInfo.cardNumber}</Text>)
                      :
                      null
                  }
                <View style={creditCardStyles.cardInfoContainer}>
                  <View style={creditCardStyles.cardInfoItem}>
                    <Text style={creditCardStyles.cardInfoLabel}>
                      Card Holder
                    </Text>
                    {
                      isEyeClosed==undefined || isEyeClosed==null
                      ?
                      cardInfo === null || cardInfo === undefined || cardInfo?.cardholderName === '' || cardInfo?.cardholderName === undefined
                      ?
                            (<Text style={creditCardStyles.cardInfoValue}></Text>)
                      :
                            (<Text style={creditCardStyles.cardInfoValue}>{cardInfo.cardholderName}</Text>)
                      :
                      isEyeClosed === true && isEyeClosedWithInState===true
                      ?
                            (<Text style={creditCardStyles.cardInfoValue}>•••• ••••</Text>)
                      :
                      isEyeClosed === true && isEyeClosedWithInState===false
                      ?
                            (<Text style={creditCardStyles.cardInfoValue}>{cardInfo.cardholderName}</Text>)
                      :
                      null
                  }
                  </View>
                      <View style={creditCardStyles.cardInfoItem}>
                          <Text style={creditCardStyles.cardInfoLabel}>Expiration</Text>

                            {
                                isEyeClosed==undefined || isEyeClosed==null
                                ?
                                cardInfo === null || cardInfo === undefined || cardInfo?.expirationDate === '' || cardInfo?.expirationDate === undefined
                                ?
                                        (<Text style={creditCardStyles.cardInfoValue}>••/••</Text>)
                                :
                                        (<Text style={creditCardStyles.cardInfoValue}>{cardInfo.expirationDate}</Text>)
                                :
                                isEyeClosed === true && isEyeClosedWithInState===true
                                ?
                                        (<Text style={creditCardStyles.cardInfoValue}>••/••</Text>)
                                :
                                isEyeClosed === true && isEyeClosedWithInState===false
                                ?
                                        (<Text style={creditCardStyles.cardInfoValue}>{cardInfo.expirationDate}</Text>)
                                :
                                null
                            }
                      </View>
                </View>
            </LinearGradient>

            <LinearGradient 
                key={7789}                   
                colors={[color1, color2]} // Gradient colors
                start={{ x: -1, y: 1 }}          // Gradient start point
                end={{ x: 1, y: 0 }}   
                style={[creditCardStyles.cardFace]}
            >
                <View style={{
                  marginTop:'20%',
                  backgroundColor:appColors.darkGray,
                  width: '100%',
                  height: scale(25),
                }}/>
                <View style={[ {position:'absolute', right: scale(20), bottom: scale(12)}]}>
                      <View style={[creditCardStyles.cardInfoItem]}>
                          <Text style={creditCardStyles.cardInfoLabel}>CVV</Text>
                            {
                                isEyeClosed==undefined || isEyeClosed==null
                                ?
                                    cardInfo === null || cardInfo === undefined || cardInfo?.cvv === '' || cardInfo?.cvv === undefined
                                ?
                                    (<Text style={[creditCardStyles.cardInfoValue, {textAlign:'center'}]}>•••</Text>)
                                :
                                    (<Text style={[creditCardStyles.cardInfoValue, {textAlign:'center'}]}>{cardInfo.cvv}</Text>)
                                :
                                isEyeClosed === true && isEyeClosedWithInState===true
                                ?
                                    (<Text style={[creditCardStyles.cardInfoValue, {textAlign:'center'}]}>•••</Text>)
                                :
                                isEyeClosed === true && isEyeClosedWithInState===false
                                ?
                                    (<Text style={[creditCardStyles.cardInfoValue, {textAlign:'center'}]}>{cardInfo.cvv}</Text>)
                                :
                                null
                            }
                      </View>
                </View>
            </LinearGradient>
        </FlipCard>
    )
  }
}

export default CreditCardView;

const creditCardStyles = StyleSheet.create({
    cardFace: {
        marginHorizontal: scale(10),
        width: scale(300),
        height: scale(180),
        backgroundColor: "white",
        borderRadius: scale(10),
        padding: scale(20),
        justifyContent: "space-between",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: scale(6) },
        shadowOpacity: 0.4,
        shadowRadius: scale(8),
        elevation: 10
    },
    logoAndIcons: {
        flexDirection: "row",
        justifyContent: "space-between",
        width: "100%",
    },
    logo: {
        width: scale(50),
        height: scale(30),
    },
    iconAndMain: {
        flexDirection: "row",
        justifyContent: "flex-end",
        gap: scale(10),
        width: "50%",
    },
    mainLabelContainer: {
        borderRadius: scale(35),
        paddingHorizontal: scale(5),
        textAlign: "center",
        alignContent: "center",
        justifyContent: "center",
    },
    mainLabel: {
        fontSize: scale(15),
        alignSelf: "center",
        color: appColors.white,
        fontWeight:'bold',
        textAlign: "center",
    },
    cardNumber: {
        fontSize: scale(16),
        letterSpacing: scale(4),
        marginBottom: scale(10),
        color:appColors.white
    },
    cardInfoContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
    },
    cardInfoItem: {
        flex: 1,
    },
    cardInfoLabel: {
        fontSize: scale(12),
        color:appColors.white
    },
    cardInfoValue: {
        fontSize: scale(12),
        fontWeight: "bold",
        color:appColors.white
    },
  });
  