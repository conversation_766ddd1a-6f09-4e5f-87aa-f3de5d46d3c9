import React, { Component } from 'react';
import { StyleSheet, Text, TextStyle, ViewStyle } from 'react-native';
import { scale } from 'react-native-size-matters';
import { appColors } from '../utils/appColors';


interface LabelProps {
  text: string;
  style?: TextStyle | ViewStyle;
}

export default class Label extends Component<LabelProps> {
  render() {
    const { text, style } = this.props;
    return (
      <Text style={[styles.label, style]}>{text}</Text>
    );
  }
}

const styles = StyleSheet.create({
  label: {
    fontSize: scale(16),
    color: appColors.black,
  },
});
