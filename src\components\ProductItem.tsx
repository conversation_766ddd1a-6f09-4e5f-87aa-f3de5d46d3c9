import React, { Component } from 'react';
import { View, Text, StyleSheet, Image, Dimensions, TouchableOpacity } from 'react-native';
import { scale } from 'react-native-size-matters';
import { Product } from '../models/Product';
import { Ionicons } from '@expo/vector-icons';
import { appColors } from '../utils/appColors';
import Animated, { FadeInDown } from 'react-native-reanimated';
import Label from './Label';
import WishlistService from '../services/WishlistService';

export interface ProductProps {
    item: Product;
    index: number;
    navigation: any;
    hotSale?: boolean;
}

export class ProductItem extends Component<ProductProps> {
    handleNavigate = () => {
        const { navigation, item } = this.props;
        console.log("Product id = "+item.id);
        navigation.navigate("ProductDetails", { id : item.id });
    };

    addToWishlist = () => {
        const { item } = this.props;
        console.log(item.id);
        WishlistService.addProductToWishlist(1, item.id).then(() => {
            item.inWishlist = true;
        });
    };

    render() {
        const { item, index, hotSale } = this.props;

        return (
            <TouchableOpacity onPress={this.handleNavigate}>
                <Animated.View
                    style={styles.container}
                    entering={FadeInDown.delay(300 + index * 100).duration(500)}
                >
                    <Image source={{ uri: item.images[0].possibleURL }} style={styles.productImage} />
                    {hotSale && (
                        <View style={styles.newLabel}>
                            <Label text={"SALE"} style={styles.newLabelText} />
                        </View>
                    )}
                    {
                        item.inWishlist === false
                        ?
                        <TouchableOpacity style={styles.bookmarkBtn} onPress={this.addToWishlist}>
                            <Ionicons name="heart-outline" color={appColors.primary} size={scale(22)} />
                        </TouchableOpacity>
                        :
                        null
                    }

                    <View style={styles.priceContainer}>
                        <Text style={styles.priceTxt}>${item.price}</Text>
                        {
                            item.rating === 0.0 || item.rating === 0
                            ?
                            null
                            :
                            <View style={styles.ratingContainer}>
                                <Ionicons name="star" color={appColors.primary} size={scale(20)} />
                                <Text style={styles.ratingTxt}>{item.rating}</Text>
                            </View>
                        }

                    </View>
                    <Text style={styles.title}>{item.name}</Text>
                </Animated.View>
            </TouchableOpacity>
        );
    }
}

const width = Dimensions.get('window').width - 20;

export default ProductItem;

const styles = StyleSheet.create({
    container: {
        width: width / 2 - scale(5),
    },
    productImage: {
        width: '100%',
        height: scale(200),
        borderRadius: scale(15),
        marginBottom: scale(10),
    },
    newLabel: {
        backgroundColor: appColors.red,
        position: 'absolute',
        top: scale(10),
        left: scale(10),
        padding: scale(3),
        borderRadius: scale(3),
        paddingHorizontal: scale(10),
    },
    newLabelText: {
        fontSize: scale(10),
        color: appColors.white,
    },
    bookmarkBtn: {
        position: 'absolute',
        right: scale(15),
        top: scale(15),
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        padding: scale(5),
        borderRadius: scale(30),
    },
    title: {
        fontSize: scale(14),
        fontWeight: '600',
        color: appColors.black,
        letterSpacing: 1.1,
    },
    priceContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: scale(8),
    },
    priceTxt: {
        fontSize: scale(18),
        fontWeight: '700',
        color: appColors.black,
    },
    ratingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 5,
    },
    ratingTxt: {
        fontSize: scale(14),
        color: appColors.darkGray,
    },
});
