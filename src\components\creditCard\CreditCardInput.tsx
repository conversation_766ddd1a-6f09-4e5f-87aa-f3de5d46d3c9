import React, { Component } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { TextInput } from "@react-native-material/core";
import { scale } from "react-native-size-matters";
import { appColors } from "../../utils/appColors";
import {
  AntDesign,
  FontAwesome,
  Ionicons,
  MaterialCommunityIcons,
  MaterialIcons,
} from "@expo/vector-icons";
import { PaymentCard } from "../../models/PaymentCard";
import cardValidator from "card-validator";
import {
  formatCardCVC,
  formatCardExpiry,
  formatCardNumber,
  toStatus,
  ValidationState,
} from "../../services/general/validation";

interface PaymentCardProps {
  cardInfo: PaymentCard;
  validation: UserInputValidation;
  cardURL: string;
  flipped: boolean;
  setCardInfo: (cardInfo: PaymentCard) => void;
  setValidation: (validation: UserInputValidation) => void;
  setCardURL: (cardURL: string) => void;
  setFlipped: (flipped: boolean) => void;
  postCard: (cardInfo: PaymentCard) => void;
}

interface UserInputValidation {
  cardHolderNameError: ValidationState;
  cardNumberError: ValidationState;
  expirationDateError: ValidationState;
  cvvError: ValidationState;
}

interface PaymentCardState {

}

class CreateCardInput extends Component<PaymentCardProps, PaymentCardState> {
  constructor(props: PaymentCardProps) {
    super(props);
    this.state = {

    };
  }

  switchCardType = (cardType: string) => {
    switch (cardType) {
      case "visa":
        return "https://chubbyshop.github.io/Temporal-DB/cards/visa.png";
      case "mastercard":
        return "https://chubbyshop.github.io/Temporal-DB/cards/master.png";
      case "maestro":
        return "https://chubbyshop.github.io/Temporal-DB/cards/maestro.png";
      default:
        return "https://chubbyshop.github.io/Temporal-DB/cards/general.png";
    }
  };

  onChangeName = (value: any) => {
    this.props.setFlipped(false);
    let { cardInfo, validation } = this.props;
    cardInfo.cardholderName = value;
    validation.cardHolderNameError = null;
    this.props.setCardInfo(cardInfo);
    this.props.setValidation(validation);
  };

  onChangeNumber = (value: any) => {
    this.props.setFlipped(false);
    const numberValidation = cardValidator.number(value);
    const cardNumberMaxLength = Math.max(
      ...(numberValidation.card?.lengths || [16])
    );
    const cardNumberGaps = numberValidation.card?.gaps || [4, 8, 12, 16];

    let { cardInfo, validation } = this.props;
    cardInfo.cardNumber = formatCardNumber(
      value,
      cardNumberMaxLength,
      cardNumberGaps
    );
    validation.cardNumberError = null;
    let cardURL = numberValidation.card?.type;
    this.props.setCardInfo(cardInfo);
    this.props.setValidation(validation);
    this.props.setCardURL(this.switchCardType(cardURL));
  };

  onChangeExpirationDate = (value: any) => {
    this.props.setFlipped(false);
    let { cardInfo, validation } = this.props;
    cardInfo.expirationDate = formatCardExpiry(value);
    validation.expirationDateError = null;
    this.props.setCardInfo(cardInfo);
    this.props.setValidation(validation);
  };

  onChangeCVV = (value: any) => {
    this.props.setFlipped(true);
    let { cardInfo, validation } = this.props;
    const numberValidation = cardValidator.number(cardInfo.cardNumber);
    const cvcMaxLength = numberValidation.card?.code.size || 3;
    cardInfo.cvv = formatCardCVC(value, cvcMaxLength);
    validation.cvvError = null;
    this.props.setCardInfo(cardInfo);
    this.props.setValidation(validation);
  };

  onSaveCard = () => {
    if (this.validateInput()) {
      let { cardInfo } = this.props;
      console.log(cardInfo);
      this.props.postCard(cardInfo);
    }
  };

  validateInput = (): boolean => {
    let { cardInfo, validation } = this.props;
    const numberValidation = cardValidator.number(cardInfo.cardNumber);
    const cvcMaxLength = numberValidation.card?.code.size || 3;

    const nameError = toStatus(
      cardValidator.cardholderName(cardInfo.cardholderName)
    );
    const numberError = toStatus(cardValidator.number(cardInfo.cardNumber));
    const expiryError = toStatus(
      cardValidator.expirationDate(cardInfo.expirationDate)
    );
    const cvcError = toStatus(cardValidator.cvv(cardInfo.cvv, cvcMaxLength));
    validation.cardHolderNameError = nameError;
    validation.cardNumberError = numberError;
    validation.expirationDateError = expiryError;
    validation.cvvError = cvcError;
    this.props.setValidation(validation);
    console.log(
      nameError + " / " + numberError + " / " + expiryError + " / " + cvcError
    );
    return (
      nameError === "valid" &&
      numberError === "valid" &&
      expiryError === "valid" &&
      cvcError === "valid"
    );
  };

  render() {
    const { cardInfo, validation } = this.props;

    const {
      cardHolderNameError,
      cardNumberError,
      cvvError,
      expirationDateError,
    } = validation;

    return (
      <React.Fragment>
        <View style={[styles.container]} testID={"asdasda"}>
          <View style={[styles.numberInput]}>
            <TextInput
              keyboardType="numeric"
              placeholder={"1234 5678 1234 5678"}
              value={cardInfo.cardNumber}
              inputStyle={
                cardNumberError === "invalid" ||
                cardNumberError === "incomplete"
                  ? { color: appColors.red, paddingTop: scale(10) }
                  : { color: appColors.black, paddingTop: scale(10) }
              }
              color={
                cardNumberError === "invalid" ||
                cardNumberError === "incomplete"
                  ? appColors.red
                  : appColors.black
              }
              inputContainerStyle={{ borderBottomColor: appColors.black }}
              maxLength={19}
              onChangeText={(text) => this.onChangeNumber(text)}
              leading={(props) => <AntDesign name="creditcard" {...props} />}
              autoCorrect={false}
              testID="CC_NUMBER"
            />
            {cardNumberError === "invalid" ||
            cardNumberError === "incomplete" ? (
              <Text style={styles.errorLabel}>
                Provided value is {cardNumberError}
              </Text>
            ) : null}
          </View>

          <View style={[styles.numberInput]}>
            <TextInput
              //style={styles.input}
              placeholder={"John Doe"}
              value={cardInfo.cardholderName}
              inputStyle={
                cardHolderNameError === "invalid" ||
                cardHolderNameError === "incomplete"
                  ? { color: appColors.red, paddingTop: scale(10) }
                  : { color: appColors.black, paddingTop: scale(10) }
              }
              color={
                cardHolderNameError === "invalid" ||
                cardHolderNameError === "incomplete"
                  ? appColors.red
                  : appColors.black
              }
              onChangeText={(text) => this.onChangeName(text)}
              leading={(props) => <AntDesign name="user" {...props} />}
              autoCorrect={false}
              testID="CC_NUMBER"
            />

            {cardHolderNameError === "invalid" ||
            cardHolderNameError === "incomplete" ? (
              <Text style={styles.errorLabel}>
                Provided value is {cardHolderNameError}
              </Text>
            ) : null}
          </View>

          <View style={[styles.extraContainer]}>
            <View style={styles.expiryInputContainer}>
              <TextInput
                keyboardType="numeric"
                //style={styles.input}
                placeholder={"MM/YY"}
                inputStyle={
                  expirationDateError === "invalid" ||
                  expirationDateError === "incomplete"
                    ? { color: appColors.red, paddingTop: scale(10) }
                    : { color: appColors.black, paddingTop: scale(10) }
                }
                color={
                  expirationDateError === "invalid" ||
                  expirationDateError === "incomplete"
                    ? appColors.red
                    : appColors.black
                }
                value={cardInfo.expirationDate}
                leading={(props) => (
                  <Ionicons name="calendar-number" {...props} />
                )}
                onChangeText={(v) => this.onChangeExpirationDate(v)}
                autoCorrect={false}
                testID="CC_EXPIRY"
              />
              {expirationDateError === "invalid" ||
              expirationDateError === "incomplete" ? (
                <Text style={styles.errorLabel}>
                  Provided value is {expirationDateError}
                </Text>
              ) : null}
            </View>

            <View style={styles.cvcInputContainer}>
              <TextInput
                keyboardType="numeric"
                //style={styles.input}
                placeholder={"CVC"}
                value={cardInfo.cvv}
                inputStyle={
                  cvvError === "invalid" || cvvError === "incomplete"
                    ? { color: appColors.red, paddingTop: scale(10) }
                    : { color: appColors.black, paddingTop: scale(10) }
                }
                color={
                  cvvError === "invalid" || cvvError === "incomplete"
                    ? appColors.red
                    : appColors.black
                }
                leading={(props) => (
                  <FontAwesome name="user-secret" {...props} />
                )}
                onChangeText={(text) => this.onChangeCVV(text)}
                autoCorrect={false}
                testID="CC_CVC"
              />
              {cvvError === "invalid" || cvvError === "incomplete" ? (
                <Text style={styles.errorLabel}>
                  Provided value is {cvvError}
                </Text>
              ) : null}
            </View>
          </View>
        </View>

        <View style={styles.saveButtonWrapper}>
          <TouchableOpacity
            style={styles.button}
            onPress={() => this.onSaveCard()}
          >
            <Text style={styles.btnTxt}>Save</Text>
            <MaterialIcons
              name="navigate-next"
              size={scale(25)}
              color={appColors.primary}
            />
          </TouchableOpacity>
        </View>
      </React.Fragment>
    );
  }
}

export default CreateCardInput;

const styles = StyleSheet.create({
  saveButtonWrapper: {
    flexDirection: "row",
    alignSelf: "stretch",
    marginVertical: scale(5),
    width: "100%",
    justifyContent: "center",
  },
  button: {
    flexDirection: "row",
    padding: scale(5),
    width: "90%",
    borderColor: appColors.gray,
    borderRadius: scale(25),
    borderWidth: StyleSheet.hairlineWidth,
    alignItems: "center",
    justifyContent: "center",
    gap: 3,
    marginBottom: scale(15),
  },
  btnTxt: {
    fontSize: scale(14),
    fontWeight: "600",
    color: appColors.black,
  },
  container: {
    padding: scale(15),
  },
  numberInput: {
    marginTop: scale(15),
  },
  extraContainer: {
    flexDirection: "row",
    marginTop: scale(15),
  },
  expiryInputContainer: {
    flex: 1,
    marginRight: scale(5),
  },
  cvcInputContainer: {
    flex: 1,
    marginLeft: scale(5),
  },
  input: {
    fontSize: scale(25),
  },
  inputLabel: {
    fontSize: scale(14),
    fontWeight: "600",
  },
  errorLabel: {
    fontSize: scale(14),
    fontWeight: "600",
    color: appColors.redOrange,
  }
});
