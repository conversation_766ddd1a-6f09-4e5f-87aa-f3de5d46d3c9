import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import * as Font from 'expo-font'; // If using Expo
import { StyleSheet, View } from 'react-native';
import { appColors } from '../utils/appColors';
import LoadingDots from "react-native-loading-dots";
import { scale } from 'react-native-size-matters';

// Define font loading status in the context
interface FontContextType {
  isFontLoaded: boolean;
}

const FontContext = createContext<FontContextType>({ isFontLoaded: false });

const loadFonts = async () => {
  await Font.loadAsync({
    'Quickless': require('../../assets/fonts/Quickless.ttf'),
    'AV_Fontimer': require('../../assets/fonts/AV_Fontimer.ttf'),
    'Jonova-Bold': require('../../assets/fonts/Jonova-Bold.ttf'),
    'Jonova-Italic': require('../../assets/fonts/Jonova-Italic.ttf'),
    'Jonova-Regular': require('../../assets/fonts/Jonova-Regular.ttf'),
    //fonts ru and aze accordingly
    'abel': require('../../assets/fonts/abel.otf'),
    'abel_bold': require('../../assets/fonts/abel_bold.otf'),
    'nunito_regular': require('../../assets/fonts/nunito_regular.ttf'),
    'nunito_bold': require('../../assets/fonts/nunito_bold.ttf'),
  });
};

interface FontProviderProps {
  children: ReactNode;
}

export const FontProvider: React.FC<FontProviderProps> = ({ children }) => {
  const [isFontLoaded, setFontLoaded] = useState(false);

  useEffect(() => {
    (async () => {
      await loadFonts();
      setFontLoaded(true);
    })();
  }, []);

  if (!isFontLoaded) {
    return (
        <View style={styles.loadingContainer}>
        <LoadingDots 
            size={scale(15)}
            colors={[appColors.primary, appColors.yellow, appColors.redOrange]}
            dots={3} 
            borderRadius={scale(15)}/>
        </View>
    );
  }

  return <FontContext.Provider value={{ isFontLoaded }}>{children}</FontContext.Provider>;
};

const styles = StyleSheet.create({
    loadingContainer:{
        marginTop:scale(200),
        justifyContent: 'center',
        alignContent:'center',
        alignSelf:'center',
        width:'20%'
    },
});

export const useFont = () => useContext(FontContext);