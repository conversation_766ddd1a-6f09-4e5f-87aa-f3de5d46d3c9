import React, {Component} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, ColorValue} from 'react-native';
import {Button, TextInput, IconButton, ActivityIndicator} from '@react-native-material/core';
import {scale} from 'react-native-size-matters';
import Container from '../../components/Container';
import Label from '../../components/Label';
import {appColors} from '../../utils/appColors';
import  Animated, {  FadeInUp }  from 'react-native-reanimated';
import {AntDesign, Entypo, Ionicons, MaterialIcons} from '@expo/vector-icons';
//import { LoginDTO } from '../../models/User';
//import AuthService from '../../services/AuthService';
import Toast, {Positions} from 'react-native-root-toast';
//import LocalUserService from '../../services/memory/LocalUserService';

interface UserInputValidation {
    emailEmpty: boolean;
    emailValidationFailed: boolean;
    passwordEmpty: boolean;
}

interface LoginProps {
    navigation : any; // You can replace `any` with a more specific type if you have defined navigation types.
}

interface LoginState {
    validation:UserInputValidation;
    loginDTO:{};
    sendingRequest: boolean;
}

class Login extends Component < LoginProps, LoginState > {
    constructor(props : LoginProps) {
        super(props);
        this.state = {
            validation:{
                emailEmpty: false,
                emailValidationFailed: false,
                passwordEmpty: false,
            },
            loginDTO:{
                email:'',
                password:''
            },
            sendingRequest: false
        };
    }

    onChangeEmail = ( text : string) => {
        this.setState((prevState) => ({
            loginDTO: {
                ...prevState.loginDTO,
                email: text
            },
            validation:{
                ...prevState.validation,
                emailEmpty: false,
                emailValidationFailed: false,
            }
        }));
    };

    onChangePass = (text : string) => {
        this.setState((prevState) => ({
            loginDTO: {
                ...prevState.loginDTO,
                password: text
            },
            validation:{
                ...prevState.validation,
                passwordEmpty: false,
            }
        }));
    };

    validateInput = () => {
        let { validation } = this.state;
        const { email, password } = this.state.loginDTO;
        let validationPassed = true;
        if (!email ) {
            validation.emailEmpty = true;
            this.setState({validation : validation})
            validationPassed = false;
        }

        if ( !password) {
            validation.passwordEmpty = true;
            this.setState({validation : validation})
            validationPassed = false;
        }   

        if(!this.validateEmail(email)){
            validation.emailValidationFailed = true;
            this.setState({validation : validation})
            validationPassed = false;
        }
    
        return validationPassed;
    }

    onSignUp = () => {
        
        // if (this.validateInput()) {
        //     let { loginDTO } = this.state;
        //     console.log(loginDTO)
        //     this.setState({sendingRequest:true});
        //     AuthService.login(loginDTO)
        //         .then((user) =>{
        //             let toast = Toast.show('Login success', {
        //                 duration: Toast.durations.SHORT,
        //                 position: Toast.positions.BOTTOM,
        //                 backgroundColor: appColors.primary,
        //                 shadow: true,
        //                 animation: true,
        //                 hideOnPress: true,
        //                 delay: 0,
        //             });
        //             setTimeout(function () {
        //                 Toast.hide(toast);
        //             }, 5000);
        //             this.setState({sendingRequest:false});
        //             //LocalUserService.postNewUser(user);
        //             this.props.navigation.navigate("MyTabsNew");
        //         })
        //         .catch((err) => {
        //             let toast = Toast.show('Login failed', {
        //                 duration: Toast.durations.SHORT,
        //                 position: Toast.positions.BOTTOM,
        //                 backgroundColor: appColors.red,
        //                 shadow: true,
        //                 animation: true,
        //                 hideOnPress: true,
        //                 delay: 0,
        //             });
        //             setTimeout(function () {
        //                 Toast.hide(toast);                        
        //             }, 5000);
        //             this.setState({sendingRequest:false});
        //         });
        // }        
    };

    validateEmail = (email: string): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    render() {
        let { sendingRequest,} = this.state;
        const { emailEmpty, emailValidationFailed, passwordEmpty } = this.state.validation;
        return (
            <Container isScrollable>
                {
                    sendingRequest
                    ?
                    <ActivityIndicator size={'large'} color={appColors.primary} />
                    :
                    <Animated.View
                        style={{
                        marginTop: scale(100),
                        backgroundColor: appColors.white,
                        padding: scale(15),
                        borderRadius: scale(5)
                        }}
                        entering={FadeInUp.delay(300).duration(700)}
                    >
                        <TextInput
                            onChangeText={(text) => this.onChangeEmail(text)}
                            keyboardType="email-address"
                            leading={props => <Entypo name="email" {...props} />}
                            inputStyle={ emailValidationFailed ? {color: appColors.red, paddingTop:scale(10)} :{ color: appColors.black, paddingTop:scale(10)} }
                            color={appColors.black}
                            placeholder="<EMAIL>"/>
                            {
                                emailEmpty
                                ?
                                <Text style={styles.errorMessage}>You need to provide email address</Text>
                                :
                                emailValidationFailed
                                ?
                                <Text style={styles.errorMessage}>This is not legitmate email address</Text>
                                :
                                null
                            } 
                        <View
                            style={{
                            paddingVertical: scale(10)
                        }}>
                            <TextInput
                                leading={props => <MaterialIcons name="password" {...props} />}
                                onChangeText={(text) => this.onChangePass(text)}
                                secureTextEntry
                                inputStyle={{paddingTop:scale(10)}}
                                color={appColors.black}
                                placeholder="Password"/>
                            {
                                passwordEmpty
                                ?
                                <Text style={styles.errorMessage}>You need to provide password</Text>
                                :
                                null
                            } 
                        </View>

                        <View style={styles.socialLoginWrapper}>
                            <View>
                                <TouchableOpacity style={styles.button} onPress={() => this.onSignUp()}>                                
                                    <Text style={styles.btnTxt}>Login</Text>
                                    <AntDesign name='login' size={20} color={appColors.primary}/>
                                </TouchableOpacity>
                            </View>
                        </View>
                        <View style={styles.divider}/>
                        <View style={styles.hasAccount}>
                            <Text style={styles.loginTxt}>Need an account? {" "}</Text>
                            <TouchableOpacity onPress={() => this.props.navigation.navigate("Register")}>
                                <Text style={styles.loginTxtSpan}>Register</Text>
                            </TouchableOpacity>                    
                        </View>
                        
                    </Animated.View>
                }
            </Container>
        );
    }
}

export default Login;

const styles = StyleSheet.create({
    socialLoginWrapper:{
        alignSelf:'stretch',    
    },
    button:{
        flexDirection:'row',
        padding:10,
        borderColor:appColors.gray,
        borderRadius:25,
        borderWidth:StyleSheet.hairlineWidth,
        // width: '100%'
        alignItems:'center',
        justifyContent:'center',
        gap:5,
        marginBottom:15,
    },
    btnTxt:{
        fontSize:scale(14),
        fontWeight:'600',
        color:appColors.black,
    },
    loginTxt:{
        fontSize: scale(12),
        color: appColors.black,
        lineHeight: 24,
    },
    hasAccount:{
        flexDirection:'row',
        justifyContent:'center',
        marginBottom: scale(30),
    },
    loginTxtSpan:{
        color:appColors.primary,
        fontWeight:'600',
        lineHeight: 24,
        fontSize:scale(16),
    },
    divider:{
        flexDirection:'row',
        justifyContent:'center',
        borderTopColor:appColors.darkGray,
        borderTopWidth:StyleSheet.hairlineWidth,
        width:'100%',
        marginVertical:15,
    },
    errorMessage:{
        color:appColors.redOrange
    }
})
