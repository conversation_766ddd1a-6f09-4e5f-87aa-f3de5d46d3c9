import React, {Component} from 'react';
import {StyleSheet, Text, Pressable, View} from 'react-native';
import { appColors } from '../utils/appColors';
import { AntDesign, Foundation } from '@expo/vector-icons';

type Props={
    onPress: Function,
    onLongPress: Function,
    isFocused: boolean,
    label: string,
    routeName: string,
}

export const icon = {
    Main: ({color, size} : {color: string, size: number}) => (
        <AntDesign name="home" color={color} size={size}/>
    ),
    CartScreen: ({color, size} : {color: string, size: number}) => (
        <AntDesign name="shoppingcart" color={color} size={size}/>
    ),
    Account: ({color, size} : {color: string, size: number}) => (
        <Foundation name="page-multiple" color={color} size={size}/>
    )
};

const TabBarButton = (props: Props) => {
    const {isFocused, label, onLongPress, onPress, routeName} = props;
    return (
    
    <Pressable
        onPress={onPress}
        onLongPress={onLongPress}
        style={styles.container}
    >
        {routeName == 'Cart' && (        
        <View style={styles.badgeWrapper}>
            <Text style={styles.badgeText}>3</Text>
        </View>
        )        
        }

        {icon[routeName]({
            color:isFocused ? appColors.primary : appColors.gray,
            size: isFocused ? 25 : 15
        })}
      <Text style={{ color: isFocused ? appColors.primary : appColors.gray }}>
        {label}
      </Text>
    </Pressable>
    )
}

export default TabBarButton;

const styles = StyleSheet.create({
    container:{
        flex: 1,
        justifyContent:'center',
        alignItems:'center',
        gap: 5,
    },
    badgeWrapper:{
        position:'absolute',
        backgroundColor:appColors.primary,
        top: -5,
        right:20,
        paddingVertical:2,
        paddingHorizontal:6,
        borderRadius:10,
        zIndex:10,
    },
    badgeText:{
        color:appColors.white,
        fontSize:12,
    }
});





