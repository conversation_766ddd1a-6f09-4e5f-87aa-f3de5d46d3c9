import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { NavigationContainer } from '@react-navigation/native';
import { Pressable, TouchableOpacity } from 'react-native-gesture-handler';
import { AntDesign, Feather, Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import i18next from 'i18next';
import Login from '../screens/auth/Login';

import Welcome from '../screens/Welcome';

import { appColors } from '../utils/appColors';

import { scale } from 'react-native-size-matters';
import AgentList from '../screens/agents/AgentList';
import ReportsAll from '../screens/reports/ReportsAll';
import Register from '../screens/auth/Register';
import AgentNew from '../screens/agents/AgentNew';
import { renderBold } from '../services/FontProvider';


const Stack = createStackNavigator();
const TabNew = createBottomTabNavigator();


export default function MyStack() {
  const { t } = useTranslation();
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="Welcome">

        <Stack.Screen
          name="Welcome"
          component={Welcome}
          options={{
            headerShown: false,
            headerLeft: () => null,
          }}
        />

        <Stack.Screen
          name="Login"
          component={Login}
          options={({ navigation }) => ({
            headerTitle: t('login.login'),
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },
            presentation: 'modal',
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()}>
                <Feather name="chevron-down" size={45} color={appColors.black} />
              </Pressable>
            ),
          })}
        />
        <Stack.Screen
          name="Register"
          component={Register}
          options={({ navigation }) => ({
            headerTitle: t('register.register'),
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold()  },
            presentation: 'modal',
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()}>
                <Feather name="chevron-down" size={45} color={appColors.black} />
              </Pressable>
            ),
          })}
        />
        <Stack.Screen name="AgentList" component={AgentList}
          options={({ navigation }) => ({
            headerTitle:  t('agents.agents'),
            headerTitleStyle: { fontSize: scale(18), fontWeight: '900' },
            presentation: 'card',
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: scale(10) }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
            headerRight: () => (
              <Pressable onPress={() => navigation.navigate('AgentNew')} style={{ marginRight: scale(10) }}>
                <AntDesign name="pluscircle" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

      <Stack.Screen name="AgentNew" component={AgentNew}
          options={({ navigation }) => ({
            headerTitle:  t('agents.newAgent'),
            headerTitleStyle: { fontSize: scale(18), fontWeight: '900' },
            presentation: 'card',
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: scale(10) }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />

      <Stack.Screen name="ReportsAll" component={ReportsAll}
          options={({ navigation }) => ({
            headerTitle: 'List of reports',
            headerTitleStyle: { fontSize: scale(18), fontWeight: '900' },
            presentation: 'card',
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerLeft: () => (
              <Pressable onPress={() => navigation.goBack()} style={{ marginLeft: 10 }}>
                <AntDesign name="arrowleft" size={35} color={appColors.black} />
              </Pressable>
            ),
            headerRight: () => (
              <Pressable onPress={() => navigation.navigate('Login')}>
                <Ionicons name="log-out-outline" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
