import * as SQLite from 'expo-sqlite';
import {agentsTableCreateQuery, usersTableCreateQuery} from './Queries';

export default class TableInitService {
    private db: SQLite.SQLiteDatabase | null = null;

    async initDB() : Promise < void > {
        this.db = await SQLite.openDatabaseAsync('Reports.db');
        return new Promise((resolve, reject) => {
            this.createUsersTable().then(resolve).catch(reject);
            this.createAgents().then(resolve).catch(reject);
        });
    }

    private async createUsersTable() : Promise < void > {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        await this.db.execAsync(usersTableCreateQuery);

        console.log('Agents table created or already exists');
    }

    private async createAgents() : Promise < void > {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        await this.db.execAsync(agentsTableCreateQuery);

        console.log('Agents table created or already exists');
    }
}
