import * as React from 'react';
import { View, Text, StyleSheet, FlatList, Dimensions, TouchableOpacity, Image } from 'react-native';
import {scale} from 'react-native-size-matters';
import { Product } from '../models/Product';
import { Category } from '../models/Category';
import { appColors } from '../utils/appColors';
import { SwiperFlatList } from 'react-native-swiper-flatlist';

export interface BannerViewProps {
    categories: Category[];
}

const width = Dimensions.get('screen').width;

const Banner = ({categories}: BannerViewProps) =>{
    return (       
        <View style={styles.container}>
        <SwiperFlatList autoplay autoplayDelay={3} autoplayLoop index={0}>
            {
                categories.map(({image, name, id}) => (
                    <TouchableOpacity style={styles.itemContainer} key={id}>
                            <Image source={{uri:image}} style={styles.itemImage}/>
                            <Text style={styles.itemTitle}>{name}</Text>
                    </TouchableOpacity>
                ))
            }
        </SwiperFlatList>
        </View>
    )
}

export default Banner;

const styles = StyleSheet.create({
    container: {
        backgroundColor: appColors.white,
        flex:1,
    },
    itemContainer:{
        alignItems:'center',
        width: width,
        flexDirection:'column',
        justifyContent:'center',
        alignContent:'center',
        alignSelf:'center'       
    },
    itemImage:{
        width:'90%',
        height:scale(150),
        resizeMode:'contain',
        borderRadius:scale(30),
        backgroundColor:appColors.lightGray
    },
    itemTitle:{
        fontSize:scale(15)
    }
});