import React, { Component } from 'react';
import { View, StyleSheet, Text, Dimensions, TouchableOpacity } from 'react-native';
import { scale } from 'react-native-size-matters';
import Animated, {FadeInDown, FadeInLeft, FadeInRight} from 'react-native-reanimated';
import { appColors } from '../utils/appColors';
import { Ionicons } from '@expo/vector-icons';


interface ErrorLoadingProps{
    errorMsg:string;
    handleError:() => void;
}

interface ErrorLoadingState{
    
}

const screenHeight = Dimensions.get('screen').height;

class ErrorLoading extends Component<ErrorLoadingProps, ErrorLoadingState> {

    constructor(props: ErrorLoadingProps) {
        super(props);
        this.state={

        }
    }

    render() {
        const {errorMsg} = this.props;
        return(
            <View style={styles.container}>
                <View style={styles.errorBox}>
                    <Animated.Text style={styles.oopsText} entering={FadeInRight.delay(300 + 100).duration(1000)}>Oops!</Animated.Text>
                    <Animated.Text style={styles.errorMsg} entering={FadeInRight.delay(300 + 300).duration(1400)}>Error: {errorMsg}</Animated.Text>           
                    <Animated.View entering={FadeInDown.delay(300 + 300).duration(1600)}>
                        <TouchableOpacity style={styles.button} onPress={() => this.props.handleError()}>
                            <Text style={styles.btnTxt}>Try again</Text>
                            <Ionicons name='reload' color={appColors.white} size={scale(20)}/>
                        </TouchableOpacity>
                    </Animated.View>
                </View>
            </View>            
        )
        

    }
}

const styles = StyleSheet.create({
    container:{
        flex:1,
        alignContent:'center',
    },
    button:{
        flexDirection:'row',
        marginVertical:scale(15),
        padding:10,
        borderColor:appColors.gray,
        backgroundColor:appColors.primary,
        borderRadius:25,
        borderWidth:StyleSheet.hairlineWidth,
        alignItems:'center',
        justifyContent:'center',
        gap:5,
    },
    btnTxt:{
        fontSize:scale(14),
        fontWeight:'600',
        color:appColors.white,
    },
    errorBox:{
        marginTop:scale(100),
        width:'80%',       
        paddingHorizontal:'10%',
        alignContent:'center',
        flexDirection:'column',
        alignSelf:'center',
        justifyContent:'center',
        alignItems:'center'
    },
    oopsText:{
        fontSize:scale(35),
        paddingBottom:scale(10),
        textAlign:'center',
        width:'100%'
    },
    errorTitle:{
        fontSize:scale(25),
        width:'100%',
        fontWeight:'bold',
        textAlign:'center',
    },
    errorMsg:{
        fontSize:scale(20),
        textAlign:'center',
        width:'100%',
    }
});

export default ErrorLoading;
