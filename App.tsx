import React, { Component } from 'react';
import 'intl-pluralrules';
import MyStack from './src/navigator/Stack';
import { FontProvider } from './src/services/FontProvider';
import ru from './src/locale/ru.json';
import az from './src/locale/az.json';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import 'intl-pluralrules';
import { StatusBar } from 'react-native';

i18n
    .use(initReactI18next)
    .init({
        resources: {
            ru: {
                translation: ru
            },
            az: {
                translation: az
            }
        },
        lng: 'az',
        fallbackLng: 'az',
        interpolation: {
            escapeValue: false
        }
    });

export default function App() {

  return (
    <FontProvider>
       <StatusBar barStyle="dark-content" translucent={true} backgroundColor="transparent"/>
      <MyStack/>
    </FontProvider>
  );

}
