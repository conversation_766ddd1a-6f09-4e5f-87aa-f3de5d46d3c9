import React, { Component, useEffect }  from 'react';
import 'intl-pluralrules';
import MyStack from './src/navigator/Stack';
import { FontProvider } from './src/services/FontProvider';
import ru from './src/locale/ru.json';
import az from './src/locale/az.json';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import 'intl-pluralrules';
import { StatusBar } from 'react-native';
import TableInitService from './src/services/sql/TableInitService';

i18n
    .use(initReactI18next)
    .init({
        resources: {
            ru: {
                translation: ru
            },
            az: {
                translation: az
            }
        },
        lng: 'ru',
        fallbackLng: 'ru',
        interpolation: {
            escapeValue: false
        }
    });
const agentService = new TableInitService();

export default function App() {

    useEffect(() => {
        agentService.initDB()
        .then(() => console.log('Database and table ready'))
        .catch(err => console.error('DB initialization failed', err));
        }, []);

  return (
    <FontProvider>
       <StatusBar barStyle="dark-content" translucent={true} backgroundColor="transparent"/>
      <MyStack/>
    </FontProvider>
  );

}
