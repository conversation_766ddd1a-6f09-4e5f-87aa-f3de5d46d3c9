import React, { Component } from 'react';
import {
  View,
  Text,
  Image,
  FlatList,
  ListRenderItemInfo,
  StyleSheet,
} from 'react-native';
import { ScaledSheet, moderateScale, verticalScale, scale } from 'react-native-size-matters';
import {withTranslation, WithTranslation} from 'react-i18next';
import { renderBold, renderRegular } from '../../services/FontProvider';

interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  image: string;
  amount: number;
}

// const { t } = useTranslation();

interface ProductCardProps  extends WithTranslation {
  item: Product;
  onIncrement: () => void;
  onDecrement: () => void;
}

class ProductCard extends Component<ProductCardProps> {
  render() {
    const { item, t, onDecrement } = this.props;
    return (
      <View style={styles.productCard}>
        <Image source={{ uri: item.image }} style={styles.productImage} />
        <View style={styles.productInfo}>
          <Text style={styles.productName}>{item.name}</Text>
          <Text style={styles.productDescription}>{item.description}</Text>
          <Text style={styles.productPrice}>           
            <Text style={styles.productPriceText}>{t('TopSales.left')}:{' '}</Text>
             {item.amount}
          </Text>
        </View>
      </View>
    );
  }
}

interface TopSalesState {
  products: Product[];
}

interface TopSalesProps extends WithTranslation {}

class TopSales extends Component<TopSalesProps, TopSalesState> {
  constructor(props) {
    super(props);
    this.state = {
      products: [
        {
          id: 1,
          name: 'Hamburger',
          description: 'Juicy beef patty on a fresh bun with all the fixings',
          price: 5.99,
          image: 'https://source.unsplash.com/900x900/?burger',
          amount: 0,
        },
        {
          id: 2,
          name: 'Pizza',
          description: 'Freshly made pizza with your choice of toppings',
          price: 9.99,
          image: 'https://source.unsplash.com/900x900/?pizza',
          amount: 2,
        },
        {
          id: 3,
          name: 'Salad',
          description: 'Fresh greens and veggies with your choice of dressing',
          price: 4.99,
          image: 'https://source.unsplash.com/900x900/?salad',
          amount: 3,
        },
        {
          id: 4,
          name: 'Fries',
          description: 'Crispy and delicious, perfect as a side or on their own',
          price: 2.99,
          image: 'https://source.unsplash.com/900x900/?fries',
          amount: 7,
        },
        {
          id: 5,
          name: 'Ice Cream',
          description: 'Rich and creamy, the perfect dessert any time of day',
          price: 3.99,
          image: 'https://source.unsplash.com/900x900/?icecream',
          amount: 10,
        },
        {
          id: 6,
          name: 'Big Hamburger',
          description: 'Juicy beef patty on a fresh bun with all the fixings',
          price: 5.99,
          image: 'https://source.unsplash.com/900x900/?burger',
          amount: 2,
        },
        {
          id: 7,
          name: 'Big Pizza ',
          description: 'Freshly made pizza with your choice of toppings',
          price: 9.99,
          image: 'https://source.unsplash.com/900x900/?pizza',
          amount: 5,
        },
        {
          id: 8,
          name: 'Big Salad',
          description: 'Fresh greens and veggies with your choice of dressing',
          price: 4.99,
          image: 'https://source.unsplash.com/900x900/?salad',
          amount: 8,
        },
        {
          id: 9,
          name: 'Big Fries',
          description: 'Crispy and delicious, perfect as a side or on their own',
          price: 2.99,
          image: 'https://source.unsplash.com/900x900/?fries',
          amount: 9,
        },
        {
          id: 10,
          name: 'Big Ice Cream',
          description: 'Rich and creamy, the perfect dessert any time of day',
          price: 3.99,
          image: 'https://source.unsplash.com/900x900/?icecream',
          amount: 12,
        },
      ],
    };
  }

  handleIncrement = (item: Product) => {
    this.setState((prevState) => ({
      products: prevState.products.map((product) =>
        product.id === item.id
          ? { ...product, amount: product.amount + 1 }
          : product
      ),
    }));
  };

  handleDecrement = (item: Product) => {
    this.setState((prevState) => ({
      products: prevState.products.map((product) =>
        product.id === item.id
          ? { ...product, amount: Math.max(0, product.amount - 1) }
          : product
      ),
    }));
  };

  renderProductItem = ({ item }: ListRenderItemInfo<Product>) => (
    <ProductCard
      item={item}
      onIncrement={() => this.handleIncrement(item)}
      onDecrement={() => this.handleDecrement(item)} 
      t={this.props.t} 
      i18n={this.props.i18n} 
      tReady={this.props.tReady}    />
  );

  render() {
    return (
      <View style={styles.container}>
        <FlatList
          data={this.state.products}
          style={styles.productList}
          renderItem={this.renderProductItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={{ paddingHorizontal: scale(16), paddingBottom: verticalScale(30) }}
        />
      </View>
    );
  }
}
export default withTranslation()(TopSales);

const styles = ScaledSheet.create({
  container: {
    flex: 1,
  },
  productList: {
    flex: 1,
    paddingTop: verticalScale(16),
  },
  productCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: moderateScale(8),
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowOffset: { width: 0, height: verticalScale(2) },
    shadowRadius: moderateScale(4),
    padding: moderateScale(16),
    marginBottom: verticalScale(16),
  },
  productImage: {
    width: moderateScale(70),
    height: moderateScale(70),
    borderRadius: moderateScale(35),
    marginRight: moderateScale(16),
  },
  productInfo: {
    flex: 1,
    marginRight: moderateScale(16),
  },
  productName: {
    fontSize: moderateScale(20),
    fontFamily: renderBold(),
    marginBottom: verticalScale(4),
  },
  productDescription: {
    fontSize: moderateScale(16),
    color: '#666',
    fontFamily: renderRegular(),
    marginBottom: verticalScale(4),
  },
  productPrice: {
    fontSize: moderateScale(18),
    fontFamily: renderBold(),
    color: '#4caf50',
  },
  productPriceText: {
    fontSize: moderateScale(16),
    fontFamily: renderRegular(),
    color: '#666',
  },
  productAmount: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  amountButton: {
    width: moderateScale(30),
    height: moderateScale(30),
    backgroundColor: '#ffa726',
    borderRadius: moderateScale(15),
    justifyContent: 'center',
    alignItems: 'center',
  },
  amountButtonText: {
    color: '#fff',
    fontSize: moderateScale(18),
  },
  amountText: {
    fontSize: moderateScale(18),
    fontWeight: 'bold',
    marginHorizontal: moderateScale(16),
  },
  continueButton: {
    position: 'absolute',
    bottom: verticalScale(16),
    left: scale(16),
    right: scale(16),
    backgroundColor: '#4caf50',
    borderRadius: moderateScale(8),
    padding: moderateScale(16),
    alignItems: 'center',
  },
  continueButtonText: {
    color: '#fff',
    fontSize: moderateScale(18),
    fontWeight: 'bold',
  },
});


