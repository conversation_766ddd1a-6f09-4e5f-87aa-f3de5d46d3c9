import React, { Component } from 'react';
import { View, ViewStyle } from 'react-native';
import Label from './Label';
import { scale } from 'react-native-size-matters';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { appColors } from '../utils/appColors';

interface EmptyProps {
  label?: string;
}

class Empty extends Component<EmptyProps> {
  render() {
    const { label } = this.props;

    return (
      <View style={containerStyle}>
        {label && (
          <Label
            style={{ fontSize: scale(23), paddingVertical: scale(20) }}
            text={label}
          />
        )}
        <MaterialCommunityIcons name='timer-sand-empty' color={appColors.primary}/>
      </View>
    );
  }
}

const containerStyle: ViewStyle = {
  flex: 1,
  justifyContent: 'center',
  alignItems: 'center',
};

export default Empty;
