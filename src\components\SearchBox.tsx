import React, { Component } from 'react';
import { View, Pressable, TextInput, StyleSheet, TouchableOpacity } from 'react-native';
import { scale } from 'react-native-size-matters';
import { appColors } from '../utils/appColors';
import Feather from 'react-native-vector-icons/Feather';
import { AntDesign } from '@expo/vector-icons';
import { SearchType } from '../screens/main/parts/Search';

interface SearchBoxProps {
  autoFocus?: boolean;
  onFoucs?: () => void;
  hideCamra?: boolean;
  onRightIconPress?: () => void;
  rightIcon?: string;
  navigation: any;
  keyword?:string;
  onClickSearch:(val:string) => void
}

interface SearchBoxState {
  searchText: string;
}

export default class SearchBox extends Component<SearchBoxProps, SearchBoxState> {
  constructor(props: SearchBoxProps) {
    super(props);
    this.state = {
      searchText: '',  // Initialize the search text state
    };
  }

  componentDidMount(): void {
      const {keyword } = this.props;
      this.setState({searchText: keyword || ''});
  }

  handleInputChange = (text: string) => {
    this.setState({ searchText: text });
  };

  handleSearchPress = () => {
    const { searchText } = this.state;
    this.props.onClickSearch(searchText);
  };

  render() {
    const { autoFocus, onFoucs, hideCamra, onRightIconPress, rightIcon, keyword } = this.props;
    const { searchText } = this.state;
    const hideCamera = false;

    return (
      <View style={styles.container}>
        <View style={styles.inputContainer}>    
          <AntDesign name="shoppingcart" size={scale(20)} color={appColors.black} />    
          <TextInput 
            autoFocus={autoFocus}
            onFocus={onFoucs}
            style={styles.textInput}
            value={searchText}  // Bind the input value to state
            onChangeText={this.handleInputChange}  // Update state when text changes
          />
          <TouchableOpacity onPress={this.handleSearchPress}>
            <Feather name="search" size={scale(20)} color={appColors.black} />
          </TouchableOpacity>
        </View>
        {hideCamera && (
          <Pressable
            onPress={onRightIconPress}
            style={styles.iconButton}>
            <Feather name={rightIcon ? rightIcon : "camera"} size={scale(18)} color={appColors.white} />
          </Pressable>
        )}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    width: scale(280),
  },
  inputContainer: {
    flex: 1,
    paddingHorizontal: scale(20),
    borderRadius: scale(10),
    alignItems: 'center',
    backgroundColor: appColors.lightGray,
    flexDirection: 'row',
    height: scale(40),
  },
  textInput: {
    flex: 1,
    paddingLeft: scale(10),
    fontSize: scale(14),
  },
  iconButton: {
    borderRadius: scale(20),
    width: scale(40),
    height: scale(40),
    backgroundColor: appColors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: scale(20),
  },
});
