import React, { Component } from 'react';
import { Animated, Easing, ImageBackground, ImageBackgroundProps, StyleSheet } from 'react-native';
//import backgroundImage from '../images/background.jpg';



interface BackgroundAnimationProps extends ImageBackgroundProps {}

interface BackgroundAnimationState {
  translateValue: Animated.Value;
}

const INPUT_RANGE_START = 0;
const INPUT_RANGE_END = 1;
const OUTPUT_RANGE_START = -281;
const OUTPUT_RANGE_END = 0;
const ANIMATION_TO_VALUE = 1;
const ANIMATION_DURATION = 25000;


class BackgroundAnimatedImage extends Component<BackgroundAnimationProps, BackgroundAnimationState> {
  constructor(props: BackgroundAnimationProps) {
    super(props);
    this.state = {
      translateValue: new Animated.Value(0),
    };
  }

  componentDidMount() {
    this.startAnimation();
  }

  startAnimation() {
    const { translateValue } = this.state;

    const translate = () => {
      translateValue.setValue(0);
      Animated.timing(translateValue, {
        toValue: ANIMATION_TO_VALUE,
        duration: ANIMATION_DURATION,
        easing: Easing.linear,
        useNativeDriver: true,
      }).start(() => translate());
    };

    translate();
  }

  render() {
    const { translateValue } = this.state;

    const translateAnimation = translateValue.interpolate({
      inputRange: [INPUT_RANGE_START, INPUT_RANGE_END],
      outputRange: [OUTPUT_RANGE_START, OUTPUT_RANGE_END],
    });

    const AnimatedImage = Animated.createAnimatedComponent(ImageBackground);

    return (
      <AnimatedImage
        resizeMode="repeat"
        style={[
          styles.background,
          {
            transform: [
              {
                translateX: translateAnimation,
              },
              {
                translateY: translateAnimation,
              },
            ],
          },
        ]}
        source={require('../images/background.jpg')}
      />
    );
  }
}

const styles = StyleSheet.create({    
    
    background: {
        position: 'absolute',
        width: 1200,
        height: 1200,
        top: 0,
        opacity: 0.8,
        transform: [
          {
            translateX: 0,
          },
          {
            translateY: 0,
          },
        ],      
      }, 
  });

export default BackgroundAnimatedImage;
