import React, { Component } from 'react';
import { View, Image, ViewStyle, ImageStyle, TouchableOpacity } from 'react-native';
import { scale } from 'react-native-size-matters';
import { appColors } from '../utils/appColors';
import Label from './Label';
import { Product } from '../models/Product';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';

interface CheckOutItemProps {
  renderBagge?: (product:Product) => React.ReactNode;
  noBg?: boolean;
  product:Product;
  navigation?:any;
}

class CheckOutItem extends Component<CheckOutItemProps> {
  render() {
    const { renderBagge, noBg, product } = this.props;
    return (
      <Animated.View
        entering={FadeInDown.delay(300+100).duration(700)}
        style={[cardStyle,
          {
          flexDirection: 'row',
          backgroundColor: noBg ? 'transparent' : appColors.lightGray,
          }
        ]}>
        <TouchableOpacity onPress={() => this.props.navigation.navigate("ProductDetails", { id : product.id })}>
        <Image
          style={imageStyle(noBg)}
          source={{uri:product.images[0].possibleURL}}
        />
        </TouchableOpacity>

        <View style={infoContainerStyle}>
          <Label text={product.name?.substring(0, 20)} style={{ fontWeight: '600' }} />
          <Label
            text={product.price+" $ "}
            style={{
              fontSize: scale(18),
              fontWeight: '500',
              color: appColors.primary,
            }}
          />          
          {renderBagge && renderBagge(product.id)}
        </View>
      </Animated.View>
    );
  }
}

const imageStyle = (noBg?: boolean): ImageStyle => ({
  height: scale(130),
  width: scale(130),
  borderRadius: scale(noBg ? 5 : 0),
});

const infoContainerStyle: ViewStyle = {
  marginLeft: scale(10),
  justifyContent: 'space-between',
  paddingVertical: scale(10),
};

const stepperContainerStyle: ViewStyle = {
  backgroundColor: appColors.lightGray,
  flexDirection: 'row',
  borderRadius: scale(5),
  overflow: 'hidden',
  alignItems: 'center',
  paddingHorizontal: scale(20),
  height: scale(35),
};

const stepperButtonStyle: ViewStyle = {
  padding: scale(10),
  opacity: scale(0.4),
};

const stepperImageStyle: ImageStyle = {
  height: scale(20),
  width: scale(20),
};

const cardStyle: ViewStyle = {
  shadowColor: '#000',
  shadowOffset: {
    width: 0,
    height: scale(6),
  },
  shadowOpacity: 0.37,
  shadowRadius: scale(7.49),
  elevation: 12,
};

export default CheckOutItem;
