import * as React from 'react';
import { View, Text, StyleSheet, FlatList, Dimensions, TouchableOpacity, Image } from 'react-native';
import {scale} from 'react-native-size-matters';
import { Product } from '../models/Product';
import { Category } from '../models/Category';
import { Ionicons } from '@expo/vector-icons';
import { appColors } from '../utils/appColors';
import Animated, { FadeInRight } from 'react-native-reanimated';
import { SearchType } from '../screens/main/parts/Search';

export interface CategoryViewProps {
    categories: Category[];
    navigation: any;
}

const CategoryView = ({categories, navigation}: CategoryViewProps) =>{
    return (
        <View style={styles.container}>
        <View style={styles.categoryWrapper}>
            <Text style={styles.categoryTitle}>Categories</Text>
            <TouchableOpacity style={styles.seeAllLink} onPress={() => navigation.navigate("AllCategories")}>
                <Text style={styles.seeAll}>See All</Text>
            </TouchableOpacity>
        </View>
        <FlatList 
            data={categories}
            horizontal
            showsHorizontalScrollIndicator={false}
            //numColumns={1}
            keyExtractor={(item) => item.id.toString()} 
            renderItem={({index, item})=>(
                <Animated.View entering={FadeInRight.delay(300 + index * 100).duration(700)}>
                <TouchableOpacity onPress={() => navigation.navigate("Search",{categoryId:item.id, pageType: SearchType.Category})}>
                    <View style={styles.itemContainer}>
                        <Image source={{uri:item.image}} style={styles.itemImage}/>
                        <Text style={styles.itemTitle}>{item.name}</Text>
                    </View>
                </TouchableOpacity>
                </Animated.View>
            )}                    
        />
        </View>
    )
}

const width = Dimensions.get('window').width - 20;

export default CategoryView;

const styles = StyleSheet.create({
    container: {
        backgroundColor: 'white',
        marginHorizontal:scale(10),
    },
    categoryWrapper:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginBottom:scale(10),
        //marginHorizontal:scale(10)
    },
    categoryTitle:{
        fontSize:scale(18),
        fontWeight:'600',
        letterSpacing:0.6,
        color:appColors.black
    },
    seeAllLink:{
        justifyContent:'center'
    },
    seeAll:{
        fontSize:scale(14),
        fontWeight:'500',
        letterSpacing:0.6,
        color:appColors.black,
        textAlign:'center'
    },
    itemContainer:{
        marginVertical:scale(10),
        gap: 5,
        alignItems:'center',
        paddingLeft:scale(10)
    },
    itemImage:{
        width:scale(50),
        height:scale(50),
        borderRadius:scale(30),
        backgroundColor:appColors.lightGray
    },
    itemTitle:{}
});