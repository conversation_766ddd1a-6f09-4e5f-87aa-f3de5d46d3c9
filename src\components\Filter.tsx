import React, { Component } from 'react';
import {
  View,
  Text,
  Pressable,
  FlatList,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import Container from '../components/Container';
import ScreenHeader from '../components/ScreenHeader';
import TitleComp from '../components/TitleComp';
import Feather from 'react-native-vector-icons/Feather';
import { scale } from 'react-native-size-matters';
import { appColors } from '../utils/appColors';
import { AntDesign, Entypo, Ionicons } from '@expo/vector-icons';
import { topBrands, colorList } from '../utils/mockData';
import { Dropdown } from 'react-native-element-dropdown';
import Label from './Label';
import Slider from '@react-native-community/slider';
import { Rating } from 'react-native-ratings';

interface FilterProps {
  navigation: any;
}

interface FilterState {
  colorFilter: boolean;
  brandsFilter: boolean;
  ratingFilter: boolean;
}

class Filter extends Component<FilterProps, FilterState> {
  constructor(props: FilterProps) {
    super(props);
    this.state = {
      colorFilter: false,
      brandsFilter: false,
      ratingFilter: false,
    };
  }

  toggleColorFilter = () => {
    this.setState((prevState) => ({
      colorFilter: !prevState.colorFilter,
    }));
  };

  toggleRatingFilter = () => {
    this.setState((prevState) => ({
      ratingFilter: !prevState.ratingFilter,
    }));
  };

  handleFocus = (isFocus: boolean) => {
    this.setState({ brandsFilter: isFocus });
  };

  handleColorFocus = (isFocus: boolean) => {
    this.setState({ colorFilter: isFocus });
  };

  render() {
    const { navigation, route } = this.props;
    const { colorFilter, brandsFilter, ratingFilter } = this.state;
    const { pageType, keyword } = route.params;
    return (
      <Container>
        <View style={styles.filterSection}>
          <Label text="Brands" style={styles.heading} />
          <Dropdown
            style={[
              dropDownStyles.dropdown,
              brandsFilter && { borderColor: appColors.primary },
            ]}
            placeholderStyle={dropDownStyles.placeholderStyle}
            selectedTextStyle={dropDownStyles.selectedTextStyle}
            iconStyle={dropDownStyles.iconStyle}
            data={topBrands}
            maxHeight={300}
            labelField="label"
            valueField="id"
            placeholder={!brandsFilter ? 'Select item' : '...'}
            value=""
            onFocus={() => this.handleFocus(true)}
            onBlur={() => this.handleFocus(false)}
            onChange={(item) => this.handleFocus(false)}
            renderLeftIcon={() => (
              <Entypo
                style={dropDownStyles.icon}
                color={brandsFilter ? appColors.primary : 'black'}
                name="address"
                size={20}
              />
            )}
          />
        </View>

        <View style={styles.filterSection}>
          <Label text="Price" style={styles.heading} />
          <Slider
            style={{ width: '100%', height: scale(20) }}
            minimumValue={5}
            maximumValue={15}
            minimumTrackTintColor={appColors.black}
            maximumTrackTintColor={appColors.gray}
            thumbTintColor={appColors.primary}
            step={1}
            value={7}
          />
        </View>

        <View style={styles.filterSection}>
          <TitleComp
            subLabel={!colorFilter ? 'Please select' : undefined}
            heading="Color"
            renderRight={() => (
              <Pressable onPress={this.toggleColorFilter}>
                <Feather
                  name={colorFilter ? 'chevron-up' : 'chevron-down'}
                  size={scale(20)}
                />
              </Pressable>
            )}
          />
    
            <View style={styles.colorOptions}>
            <Dropdown
            style={[
              dropDownStyles.dropdown,
              colorFilter && { borderColor: appColors.primary },
            ]}
            placeholderStyle={dropDownStyles.placeholderStyle}
            selectedTextStyle={dropDownStyles.selectedTextStyle}
            iconStyle={dropDownStyles.iconStyle}
            data={colorList}
            maxHeight={300}
            labelField="label"
            valueField="id"
            placeholder={!colorFilter ? 'Select item' : '...'}
            value=""
            renderItem={(item) => {
              return(
                <View style={dropDownStyles.flexRow}>
                  <View style={[dropDownStyles.square, {backgroundColor:item.color}]}/>
                  <Text style={dropDownStyles.colorText}>{item.label}</Text>
                </View>
              )
            }}
            onFocus={() => this.handleColorFocus(true)}
            onBlur={() => this.handleColorFocus(false)}
            onChange={(item) => this.handleColorFocus(false)}
            renderLeftIcon={() => (
              <Ionicons
                style={dropDownStyles.icon}
                color={colorFilter ? appColors.primary : 'black'}
                name="color-fill"
                size={20}
              />
            )}
          />
            </View>
          
        </View>

        <View style={styles.filterSection}>
          <TitleComp
            subLabel={!ratingFilter ? 'Please select' : undefined}
            heading="Rating"
            renderRight={() => (
              <Pressable onPress={this.toggleRatingFilter}>
                <Feather
                  name={ratingFilter ? 'chevron-up' : 'chevron-down'}
                  size={scale(20)}
                />
              </Pressable>
            )}
          />
          {ratingFilter && (
            <View style={styles.colorOptions}>
              <FlatList
                horizontal
                data={[1, 2, 3, 4, 5]}
                keyExtractor={(item) => String(item)}
                ItemSeparatorComponent={() => <View style={styles.itemSeparator} />}
                renderItem={({ item }) => (
                  <TouchableOpacity>
                    <Rating
                      ratingCount={5}
                      startingValue={item}
                      imageSize={scale(30)}
                      readonly
                    />
                  </TouchableOpacity>
                )}
                showsHorizontalScrollIndicator={false}
              />
            </View>
          )}
        </View>

        <View style={styles.socialLoginWrapper}>
          <TouchableOpacity
            style={styles.button}
            onPress={() => navigation.navigate('Search', {pageType: pageType, keyword:keyword})}
          >
            <Text style={styles.btnTxt}>Apply</Text>
            <AntDesign name="search1" size={20} color={appColors.primary} />
          </TouchableOpacity>
        </View>
      </Container>
    );
  }
}

export default Filter;

const dropDownStyles = StyleSheet.create({
  dropdown: {
    height: 50,
    borderColor: 'gray',
    borderWidth: 0.5,
    borderRadius: 8,
    paddingHorizontal: 8,
  },
  icon: {
    marginRight: 5,
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 16,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  flexRow:{
    flexDirection:'row',
    alignContent:'center',
    paddingVertical:scale(3),
    gap:scale(3)
  },
  colorText:{
    textAlign:'center',
    alignSelf:'center',
    fontSize:scale(12)
  },
  square:{
    height:scale(20),
    width:scale(20)
  }
});

const styles = StyleSheet.create({
  filterSection: {
    paddingVertical: scale(20),
  },
  colorOptions: {
    paddingVertical: scale(20),
  },
  itemSeparator: {
    padding: scale(10),
  },
  heading: {
    fontSize: scale(20),
    fontWeight: '600',
  },
  colorBox: {
    height: scale(40),
    width: scale(40),
    borderRadius: scale(10),
  },
  socialLoginWrapper: {
    alignSelf: 'center',
    width: '90%',
    backgroundColor: appColors.white,
  },
  button: {
    flexDirection: 'row',
    padding: 10,
    borderColor: appColors.gray,
    borderRadius: 25,
    borderWidth: StyleSheet.hairlineWidth,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
    gap: 5,
  },
  btnTxt: {
    fontSize: scale(14),
    fontWeight: '600',
    color: appColors.black,
  },
});
