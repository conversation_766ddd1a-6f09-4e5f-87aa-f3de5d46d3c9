import React from 'react';
import {appColors} from './appColors';
import { Foundation, Ionicons, MaterialCommunityIcons, MaterialIcons, Octicons } from '@expo/vector-icons';
import { Category } from '../models/Product';

export const features = [
  'Always up-to-date React Native scaffolding',
  'Modular and well-documented structure for application code',
  'Redux for state management',
  'React Navigation for simple navigation',
  'Dropdown Alert Helper for showing success/error/info message',
  'Basic custom components like Text input,Label and picker select etc',
];
export const starterIntro = [
  'We love building apps with React Native, because it helps us create high quality products for both major mobile platforms quickly and cost-effectively.',
  'Getting started on a new app just takes too long. Most apps need the same basic building blocks and developer infrastructure, and we are bored of reinventing the wheel time and time again.',
  'This Starter Kit reflects the best practices of React Native development we have discovered while building real-world applications for our customers. It is opinionated about tooling, patterns and development practices. It might not be a one-size-fits-all solution for everyone, but feel free to customize it for your needs, or just take inspiration from it.',
];
export const bestSellersList = [
  {
    id:1,
    name: 'BeoPlay Speaker',
    description: '<PERSON> and <PERSON><PERSON><PERSON><PERSON>',
    price: '755',
    image: require('../images/products/2.png'),
  },
  {
    id:2,
    name: 'Wrist watch',
    description: 'Tag Heuer',
    price: '499',
    image: require('../images/products/2.png'),
  },
  {
    id:3,
    name: 'Nike FIT Sleeve',
    description: 'Nike Dri-FIT longer.',
    price: '1500',
    image: require('../images/products/2.png'),
  },
  {
    id: 4,
    name: 'BeoPlay Speaker',
    description: 'Bang and Olufsen',
    price: '755',
    image: require('../images/products/2.png'),
  }
];

export const productDetail = {
  name: 'Leather Wrist watch',
  detail:
    'Nike Dri-FIT is a polyester fabric designed to help you keep dry so you can more comfortably work harder, longer.',
  price: '$499',
  size: 'XL',
  color: 'blue',
  image: require('../images/products/2.png'),
  isFav: false,
};

export const reviews = [
  {
    name: 'Amusoftech',
    detail: 'Wonderful jean, perfect gift for my girl for our anniversary!',
    count: 4,
    image: require('../images/rate/1.png'),
  },
  {
    name: 'Aman Deep',
    detail: 'Nike Dri-FIT is a polyester fabric designed to help you ',
    count: 3,
    image: require('../images/rate/1.png'),
  },
];

export const categories: Category[] = [
  {
    id: 1,
    name: "Clothes",
    image: require("../images/categories/Clothes.jpeg")
  },
  {
    id: 2,
    name: "Electronics",
    image: require("../images/categories/Electronics.jpeg")
  },
  {
    id: 3,
    name: "Furniture",
    image: require("../images/categories/Furniture.jpeg")
  },
  {
    id: 4,
    name: "Shoes",
    image: require("../images/categories/Shoes.jpg")
  },
  {
    id: 5,
    name: "Jewellery",
    image: require("../images/categories/Jewellery.jpg")
  },
  {
    id: 6,
    name: "Miscellaneous",
    image: require("../images/categories/Miscellaneous.jpeg")
  }
];

export const categoriesList = [
  {
    label: 'Men',
    Icon: () => <Ionicons name="man" size={40} color={appColors.primary} />,
  },
  {
    label: 'Women',
    Icon: () => <Ionicons name="woman" size={40} color={appColors.primary} />,
  },
  {
    label: 'Devices',
    Icon: () => <Octicons name="device-desktop" size={40} color={appColors.primary} />,
  },
  {
    label: 'Gaming',
    Icon: () => <Ionicons name="game-controller" size={40} color={appColors.primary} />,
  },
  {
    label: 'Gadget',
    Icon: () => <MaterialIcons name="health-and-safety" size={40} color={appColors.primary} />,
  },
];
export const topBrands = [
  {
    id:1,
    label: 'Apple Inc',
    products: '5693 Products',
    icon: 'logo-apple',
  },
  {
    id:2,
    label: 'Google Llc',
    products: '6613 Products',
    icon: 'logo-google',
  },
  {
    id:3,
    label: 'Samsung',
    products: '70 Products',
    icon: 'logo-google',
  },
  {
    id:4,
    label: 'Nokia',
    products: '123 Products',
    icon: 'logo-google',
  },
  {
    id:5,
    label: 'Lenovo Llc',
    products: '4000 Products',
    icon: 'logo-google',
  },
];
export const recentSearches = [
  'Amusoftech',
  'Shoes',
  'Caps',
  'Apple',
  'Google',
  'Macbook',
  'Sport weares',
];

export const deliveryTypes = [
  {
    label: 'Standard Delivery',
    subLabel: 'Order will be delivered between 3 - 5 business days',
    selected: true,
  },
  {
    label: 'Next Day Delivery',
    subLabel:
      'Place your order before 6pm and your items will be delivered the next day',
    selected: false,
  },
  {
    label: 'Nominated Delivery',
    subLabel:
      'Pick a particular date from the calendar and order will be delivered on selected date',
    selected: false,
  },
];
export const paymentMethods = ['dollar-sign', 'credit-card', 'layout'];

export const profileKeys=[
  {
    label:"Edit Profile",
    description:"Reset password, edit your personal information",
    icon:"edit",
    route:"EditUser"
  },
  {
    label:"Shipping Address",
    description:"Your current addresses",
    icon:"address-card",
    route:"Address"
  },
  {
    label:"Wishlist",
    description:"What you want to buy",
    icon:"gift",
    route :"WishList"
  },
  {
    label:"Order History",
    description:"Take a look at your orders",
    icon:"history",
    route: "PastOrders"
  },
  // {
  //   label:"Track Order",
  //   description:"Track your current orders",
  //   icon:"truck",
  //   route: "Orders"
  // },
  {
    label:"Cards",
    description:"Your card based information",
    icon:"credit-card",
    route: "CreditCard"
  },
  {
    label:"Reviews",
    description:"Reviews which you left",
    icon:"pencil",
    route: "AllReviews",
  },
  {
    label:"Company",
    description:"If you want to start selling your own products",
    icon:"building-o",
    route: "CompanyInit"
  },
  {
    label:"Notifications",
    description:"",
    icon:"bell"
  },
  {
    label:"About Us",
    description:"Who are we?",
    icon:"shopping-basket",
    route: "AboutUs"
  },
  {
    label:"Sign Out",
    description:"",
    icon:"sign-out",
    route: "Welcome"
  }
]

export const orderList =[
  {
    label: 'AMU - 9249296 - N',
    amount: '$3503',
    status: 'In transit',
    color: 'yellow',
  },
  {
    label:"OD - ********* - N",
    amount:"$3453",
    status:"Delivered",
    color:"primary"
  },
  {
    label:"OD - ********* - N",
    amount:"$3503",
    status:"Delivered",
    color:"primary"
  },
  {
    label:"OD - ********* - N",
    amount:"$4453",
    status:"Delivered",
    color:"primary"
  }, 
  /* {
    label:"",
    amount:"",
    status:"",
    color:""
  },
  {
    label:"",
    amount:"",
    status:"",
    color:""
  } */
]


export const colorList = 
[
  {
    id:1,
    color:"red",
    label:"Red"
  },
  {
    id:2,
    color:"green",
    label:"Green"
  },
  {
    id:3,
    color:"blue",
    label:"Blue"
  },
  {
    id:4,
    color:"orange",
    label:"Orange"
  },
  {
    id:5,
    color:"yellow",
    label:"Yellow"
  },
  {
    id:6,
    color:"pink",
    label:"Pink"
  },
]


export const sizes:string[] = ["XS", "S", "M", "L", "XL", "XXL","3XL"];
export const colors:string[] = [appColors.black, appColors.darkGray, appColors.lightGray, appColors.red, appColors.redOrange];

