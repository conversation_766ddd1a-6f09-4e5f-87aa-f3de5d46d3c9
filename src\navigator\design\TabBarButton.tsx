import React from 'react';
import {StyleSheet,  Pressable} from 'react-native';
import { AntDesign, Enty<PERSON>, Feather, FontAwesome, FontAwesome6, MaterialIcons } from '@expo/vector-icons';
import { scale } from 'react-native-size-matters';

type Props={
    onPress: Function,
    onLongPress: Function,
    isFocused: boolean,
    label: string,
    routeName: string,
}

export const icon = {
    AgentList: ({color, size} : {color: string, size: number}) => (
        <MaterialIcons name="support-agent" color={color} size={size}/>
    ),
    ReportMain: ({color, size} : {color: string, size: number}) => (
        <AntDesign name="barschart" color={color} size={size}/>
    ),
    StockMain: ({color, size} : {color: string, size: number}) => (
        <MaterialIcons name="inventory" color={color} size={size}/>
    ),
    PersonalInit: ({color, size} : {color: string, size: number}) => (
        <FontAwesome6 name="person" color={color} size={size}/>
    ),
};

const TabBarButton = (props: Props) => {
    const {isFocused, onLongPress, onPress, routeName} = props;
    return (
    
    <Pressable
        onPress={onPress}
        onLongPress={onLongPress}
        style={[styles.container,{
           
        }]}
    >

        {icon[routeName]({
            color:isFocused ? 'black' : 'gray',
            size: isFocused ? scale(25) : scale(15)
        })}
    </Pressable>
    )
}

export default TabBarButton;

const styles = StyleSheet.create({
    container:{
        flex: 1,
        justifyContent:'center',
        alignItems:'center',
        gap: 5,
    },
});





