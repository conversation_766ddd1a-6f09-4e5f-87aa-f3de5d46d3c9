import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { TabBar } from './design/TabBar';
import { useTranslation } from 'react-i18next';
import AgentList from '../screens/agents/AgentList';
import ReportMain from '../screens/reports/ReportMain';
import StockMain from '../screens/stock/StockMain';
import PersonalInit from '../screens/personal/PersonalInit';
import { AntDesign } from '@expo/vector-icons';
import { Pressable } from 'react-native';
import { scale } from 'react-native-size-matters';
import { renderBold } from '../services/FontProvider';
import { appColors } from '../utils/appColors';


const TabNew = createBottomTabNavigator();

export const MyTabsNew = () => {
    const { t } = useTranslation();
    return(  
    <TabNew.Navigator tabBar={(props) => <TabBar {...props} />}>
      <TabNew.Screen
        name="AgentList"
        component={AgentList}
          options={({ navigation }) => ({
            headerTitle:  t('agents.agents'),
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },           
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
            headerRight: () => (
              <Pressable onPress={() => navigation.navigate('AgentNew')} style={{ marginRight: scale(10) }}>
                <AntDesign name="pluscircle" size={35} color={appColors.black} />
              </Pressable>
            ),
          })}
      />
      <TabNew.Screen
        name="ReportMain"
        component={ReportMain}
          options={({ navigation }) => ({
            headerTitle:  t('reportMain.title'),
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },           
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
          })}
      />
      <TabNew.Screen
        name="StockMain"
        component={StockMain}
        options={({ navigation }) => ({
            headerTitle:  t('stockMain.title'),
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },           
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
          })}
      />
        <TabNew.Screen
        name="PersonalInit"
        component={PersonalInit}
        options={({ navigation }) => ({
            headerTitle:  t('personal.employee'),
            headerTitleStyle: { fontSize: scale(24), fontFamily: renderBold() },           
            headerShadowVisible: false,
            headerStyle: {
              backgroundColor: 'transparent'
            },
          })}
      />
    </TabNew.Navigator>
    )
  };