import React, { Component } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { scale } from 'react-native-size-matters';
import Checkbox from './Checkbox';
import Label from './Label';
import TitleComp from './TitleComp';
import { appColors } from '../utils/appColors';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { AntDesign, MaterialCommunityIcons } from '@expo/vector-icons';
import { NavigationContainerProps } from '@react-navigation/native';
import { Address } from '../models/Address';

interface SelectAbleProps extends NavigationContainerProps {
    selected: boolean;
    item: Address;
    removeAddress: (userId: number, addressId: number) => void; // Corrected typing
    navigation:any;
}

interface SelectableState{

}



class SelectAble extends Component<SelectAbleProps, SelectableState> {
    
    constructor(props: SelectAbleProps) {
        super(props);
        this.state = {
        };
    }

    render() {
        const { selected, item } = this.props;
        const { receiver, street, city, country, postcode, addressType, id } = item;

        return (
            <View style={styles.container}>
                <View style={styles.headerStyle}>
                <TitleComp heading={addressType} />
                <TouchableOpacity style={styles.editOpacity} onPress={() => {this.props.navigation.navigate("CreateAddress", {address:item})}}>
                    <AntDesign name='edit' color={appColors.primary} size={scale(25)}/>
                </TouchableOpacity>
                <TouchableOpacity style={styles.editOpacity} onPress={() => {this.props.removeAddress(1, id)}}>
                    <AntDesign name='delete' color={appColors.primary} size={scale(25)}/>
                </TouchableOpacity>
                </View>
                <View style={styles.row}>
                    <View style={styles.column}>
                        <View style={styles.labelContainer}>
                            <Label text={receiver} style={styles.subLabel} />
                        </View>
                        <View style={styles.labelContainer}>
                            <Label text={street+', '+postcode} style={styles.subLabel} />
                        </View>
                        <View style={styles.labelContainer}>
                            <Label text={city+', '+country} style={styles.subLabel} />
                        </View>
                    </View>
                    <View style={styles.checkBoxContainer}>
                            {/* <Checkbox onPress={this.handleSelect} isChecked={selected} /> */}
                            <MaterialCommunityIcons name={addressType.icon} size={scale(60)} color={appColors.primary}/>
                    </View>
                </View>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingVertical: scale(20),
    },
    headerStyle:{
        flexDirection:'row',
        gap: scale(10),
    },
    editOpacity:{
        paddingHorizontal:scale(3),
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    column: {
        flexDirection: 'column',
        justifyContent: 'space-between',
    },
    labelContainer: {
        flex: 1,
        paddingVertical: scale(1),
    },
    subLabel: {
        fontSize: scale(13),
    },
    checkBoxContainer: {
        flex: 1,
        alignItems: 'flex-end',
    },
});

export default SelectAble;
