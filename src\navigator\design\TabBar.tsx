
import { View, StyleSheet, LayoutChangeEvent } from 'react-native';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import TabBarButton from './TabBarButton';
import  Animated, {  useAnimatedStyle, useSharedValue, withTiming }  from 'react-native-reanimated';
import { useEffect, useState } from 'react';
import { scale } from 'react-native-size-matters';

export function TabBar({ state, descriptors, navigation }: BottomTabBarProps) {
    const [dimensions, setDimensions] = useState({height:20, width:100});

    const buttonWidth = dimensions.width / state.routes.length;

    const onTabBarLayout = (e : LayoutChangeEvent) => {
        setDimensions({
            height:e.nativeEvent.layout.height,
            width:e.nativeEvent.layout.width,
        })
    }

    useEffect(() => {
        tabPositionX.value = withTiming(buttonWidth * state.index), 
        {duration: 200}
    },[state.index]);

    const tabPositionX = useSharedValue(0);

    const animatedStyle = useAnimatedStyle(() => {
        return {
            transform : [{
                translateX: tabPositionX.value
            }]
        };
    });

    return (
      <View onLayout={onTabBarLayout} style={styles.tabBar}>
        <Animated.View style={[animatedStyle, styles.animation, {width:buttonWidth / 1.2 }]} />
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key];
          const label =
            options.tabBarLabel !== undefined
              ? options.tabBarLabel
              : options.title !== undefined
                ? options.title
                : route.name;
  
          const isFocused = state.index === index;
  
          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });
  
            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name, route.params);
            }
          };
  
          const onLongPress = () => {
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            });
          };
  
          return (
            <TabBarButton key={route.name} onPress={onPress} onLongPress={onLongPress} isFocused={isFocused} routeName={route.name} />
          );
        })}
      </View>
    );
  }


const styles = StyleSheet.create({
    tabBar:{
        flexDirection: 'row',
        paddingTop:scale(16),
        paddingBottom:scale(20),
        backgroundColor: 'white',
    },
    animation:{
        position:'absolute',
        justifyContent:'center',
        backgroundColor:'black',
        top:0,
        left:scale(5),
        height:scale(1),
    }
});
  