import React, { Component } from 'react';
import {
  View,
  Text,
  Image,
  FlatList,
  ListRenderItemInfo,
  StyleSheet,
  TextInput,
} from 'react-native';
import { ScaledSheet, moderateScale, verticalScale, scale } from 'react-native-size-matters';
import {withTranslation, WithTranslation} from 'react-i18next';
import { renderBold, renderRegular } from '../services/FontProvider';
import { Product } from '../model/Product';



// const { t } = useTranslation();

interface ProductCardProps  extends WithTranslation {
  item: Product;
  onIncrement: () => void;
  onDecrement: () => void;
}

class ProductCard extends Component<ProductCardProps> {
  render() {
    const { item, t, onDecrement } = this.props;
    return (
      <View style={styles.productCard}>
        <Image source={{ uri: item.image }} style={styles.productImage} />
        <View style={styles.productInfo}>
          <Text style={styles.productName}>{item.name}</Text>
          <Text style={styles.productDescription}>{item.description}</Text>
          <Text style={styles.productPrice}>           
            <Text style={styles.productPriceText}>{t('leftovers.left')}:{' '}</Text>
             {item.amount}
          </Text>
        </View>
      </View>
    );
  }
}

interface ProductsState {
  searchQuery : string;
}

interface ProductsProps extends WithTranslation {
    products: Product[];
}

class Products extends Component<ProductsProps, ProductsState> {
  constructor(props) {
    super(props);
    this.state = {
        searchQuery: '',
    };
  }


    searchFilter = (item : Product): boolean => {
        const query = this
            .state
            .searchQuery
            .toLowerCase();
        return item
            .name
            .toLowerCase()
            .includes(query);
    };

  renderProductItem = ({ item }: ListRenderItemInfo<Product>) => (
    <ProductCard
      item={item}
      onIncrement={() => {}}
      onDecrement={() => {}} 
      t={this.props.t} 
      i18n={this.props.i18n} 
      tReady={this.props.tReady}    />
  );

  render() {
        const { searchQuery } = this.state;
        const {  products} = this.props;
        const filteredProducts = products.filter(this.searchFilter);
    return (
      <View style={styles.container}>
            <TextInput
                style={styles.searchInput}
                placeholder="Search..."
                value={searchQuery}
                onChangeText={(text) => this.setState({searchQuery: text})}/>
        <FlatList
          data={filteredProducts}
          style={styles.productList}
          renderItem={this.renderProductItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={{ paddingVertical: scale(16), paddingBottom: verticalScale(30) }}
        />
      </View>
    );
  }
}
export default withTranslation()(Products);

const styles = ScaledSheet.create({
  container: {
    flex: 1,
    padding: moderateScale(10),
  },
  searchInput: {
        height: verticalScale(40),
        borderWidth: moderateScale(2),
        borderRadius: moderateScale(5),
        borderColor: '#A9A9A9',
        marginBottom: verticalScale(10),
        paddingHorizontal: moderateScale(10)
    },
  productList: {
    flex: 1,
    paddingTop: verticalScale(5),
  },
  productCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#ffffffff',
    borderRadius: moderateScale(8),
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowOffset: { width: 0, height: verticalScale(2) },
    shadowRadius: moderateScale(4),
    padding: moderateScale(16),
    marginBottom: verticalScale(16),
  },
  productImage: {
    width: moderateScale(70),
    height: moderateScale(70),
    borderRadius: moderateScale(35),
    marginRight: moderateScale(16),
  },
  productInfo: {
    flex: 1,
    marginRight: moderateScale(16),
  },
  productName: {
    fontSize: moderateScale(20),
    fontFamily: renderBold(),
    marginBottom: verticalScale(4),
  },
  productDescription: {
    fontSize: moderateScale(16),
    color: '#666',
    fontFamily: renderRegular(),
    marginBottom: verticalScale(4),
  },
  productPrice: {
    fontSize: moderateScale(18),
    fontFamily: renderBold(),
    color: '#4caf50',
  },
  productPriceText: {
    fontSize: moderateScale(16),
    fontFamily: renderRegular(),
    color: '#666',
  },
  productAmount: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  amountButton: {
    width: moderateScale(30),
    height: moderateScale(30),
    backgroundColor: '#ffa726',
    borderRadius: moderateScale(15),
    justifyContent: 'center',
    alignItems: 'center',
  },
  amountButtonText: {
    color: '#fff',
    fontSize: moderateScale(18),
  },
  amountText: {
    fontSize: moderateScale(18),
    fontWeight: 'bold',
    marginHorizontal: moderateScale(16),
  },
  continueButton: {
    position: 'absolute',
    bottom: verticalScale(16),
    left: scale(16),
    right: scale(16),
    backgroundColor: '#4caf50',
    borderRadius: moderateScale(8),
    padding: moderateScale(16),
    alignItems: 'center',
  },
  continueButtonText: {
    color: '#fff',
    fontSize: moderateScale(18),
    fontWeight: 'bold',
  },
});


