import * as React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import {scale} from 'react-native-size-matters';
import { Product } from '../models/Product';
import { appColors } from '../utils/appColors';
import ProductItem from './ProductItem';
import { SearchType } from '../screens/main/parts/Search';


export interface ProductListProps {
    products: Product[];
    title:string;
    path?:string;
    navigation:any;
    hotSale?:boolean;
    pageType?: SearchType;
}

const ProductList = ({products, title, path, navigation, hotSale, pageType}: ProductListProps) =>{
    return (
        <View style={styles.container}>
            <View style={styles.categoryWrapper}>
                <Text style={styles.categoryTitle}>{title}</Text>
                <TouchableOpacity style={styles.seeAllLink} onPress={() => navigation.navigate('Search', { pageType:pageType })}>
                    <Text style={styles.seeAll}>See All </Text>
                </TouchableOpacity>
            </View>
            <FlatList 
                data={products}
                columnWrapperStyle={{justifyContent:'space-between', marginBottom:scale(15)}}
                numColumns={2}
                scrollEnabled={false}
                keyExtractor={(item) => item.id.toString()} 
                renderItem={({index, item})=>(
                    <ProductItem item={item} index={index} hotSale={hotSale} navigation={navigation}/>
                )}                    
            />
        </View>
    )
}

export default ProductList;

const styles = StyleSheet.create({
    container: {
        marginHorizontal:scale(10),
        backgroundColor: 'white'
    },
    categoryWrapper:{
        flexDirection:'row',
        justifyContent:'space-between',
        marginBottom:scale(10)
    },
    categoryTitle:{
        fontSize:scale(18),
        fontWeight:'600',
        letterSpacing:0.6,
        color:appColors.black
    },
    seeAllLink:{
        justifyContent:'center'
    },
    seeAll:{
        fontSize:scale(14),
        fontWeight:'500',
        letterSpacing:0.6,
        color:appColors.black,
        textAlign:'center'
    },
});