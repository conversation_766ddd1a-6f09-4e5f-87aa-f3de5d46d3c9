import React, { Component } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { appColors } from '../utils/appColors';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { scale } from 'react-native-size-matters';
import SearchBox from './SearchBox';
import { Feather } from '@expo/vector-icons';

interface HeaderProps {
    navigation:any;
    goBack?:boolean;
    keyword?:string;
    onClickSearch:(val:string) => void
}

interface HeaderState {

}

const Header = (props: HeaderProps) => {
    
    const insets = useSafeAreaInsets();
    console.log(props.goBack);

    return (
      <View style={[styles.container,{paddingTop: insets.top}]}>
        {
            props.goBack === true
            ?
            <TouchableOpacity onPress={() => props.navigation.goBack()}>
                <Feather name="chevron-left" size={45} color={appColors.black} />
            </TouchableOpacity>
            :
            <Text style={styles.logo}>CS</Text>
        }        
        <SearchBox onClickSearch={(text) => props.onClickSearch(text)} keyword={props.keyword} navigation={props.navigation}/>
      </View>
    );
  
}

const styles = StyleSheet.create({

    container: {
        flexDirection:'row',
        justifyContent:'space-evenly',
        alignItems:'center',
        backgroundColor:appColors.white,
        paddingHorizontal:10,
        paddingBottom:20,
        gap:15,
        width:'100%',
        height:scale(100),
    },
    logo:{
        fontSize:24,
        fontWeight:'700',
        color:appColors.primary
    },
    searchText:{
        textAlign:'center',
        color:appColors.white,
        alignSelf:'center',
        fontSize:20,
    },
    searchBar:{
        flex:1,
        backgroundColor: appColors.primary,
        flexDirection:'row',
        paddingLeft:10,
        paddingVertical: 8,
        borderRadius:5,
        justifyContent:'space-between',
        width:'90%',
    },
});

export default Header;
