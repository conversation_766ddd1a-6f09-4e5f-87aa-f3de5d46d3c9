{"name": "report1C", "version": "4.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start -c", "dev-start": "npx expo start --dev-client", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@expo/webpack-config": "^19.0.0", "@likashefqet/react-native-image-zoom": "^4.3.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/checkbox": "^0.5.17", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/slider": "4.5.6", "@react-native-material/core": "^1.3.7", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^6.1.0", "@types/react": "~19.0.10", "axios": "^1.6.0", "card-validator": "^10.0.0", "expo": "53.0.19", "expo-checkbox": "~4.1.4", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-linear-gradient": "~14.1.5", "expo-router": "~5.1.3", "expo-splash-screen": "~0.30.10", "expo-sqlite": "~15.2.14", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "i18next": "^23.2.6", "intl-pluralrules": "^2.0.0", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^14.1.2", "react-native": "0.79.5", "react-native-alert-notification": "^0.4.0", "react-native-country-picker-modal": "^2.0.0", "react-native-credit-card-input": "^1.0.0", "react-native-element-dropdown": "^2.12.2", "react-native-flip-card": "^3.5.7", "react-native-gesture-handler": "~2.24.0", "react-native-google-mobile-ads": "^14.1.0", "react-native-image-pan-zoom": "^2.1.12", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-loading-dots": "^1.3.5", "react-native-modal": "^13.0.1", "react-native-multiple-select": "^0.5.12", "react-native-picker-select": "^8.0.4", "react-native-progress-stepper": "^1.0.4", "react-native-ratings": "^8.1.0", "react-native-reanimated": "~3.17.4", "react-native-root-toast": "^3.6.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-size-matters": "^0.4.0", "react-native-stepper-view": "^0.2.6", "react-native-svg": "15.11.2", "react-native-swipe-list-view": "^3.2.9", "react-native-swiper-flatlist": "^3.2.5", "react-native-touch-ripple": "^0.0.4", "react-native-ui-stepper": "^1.2.4", "react-native-vector-icons": "^10.1.0", "react-native-web": "^0.20.0", "react-redux": "^7.2.2", "redux": "^4.0.5", "redux-persist": "^6.0.0", "redux-saga": "^1.1.3", "rn-animated-snackbar": "^1.0.1", "rn-range-slider": "^2.1.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/jest": "^29.5.12", "@types/react-test-renderer": "^18.0.7", "jest": "^29.2.1", "jest-expo": "~53.0.9", "typescript": "~5.8.3"}, "private": true}