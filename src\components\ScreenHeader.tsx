import React, { Component } from 'react';
import { View, Pressable, StyleSheet } from 'react-native';
import { scale } from 'react-native-size-matters';
import Feather from 'react-native-vector-icons/Feather';
import { appColors } from '../utils/appColors';
import Label from './Label';
import { NavigationProp, ParamListBase } from '@react-navigation/native';

interface ScreenHeaderProps {
    navigation?: NavigationProp<ParamListBase>;
    backIcon?: string;
    rightIcon?: string;
    label: string;
    showSearch?: boolean;
}

class ScreenHeader extends Component<ScreenHeaderProps> {
    render() {
        const { navigation, backIcon, rightIcon, label, showSearch } = this.props;

        return (
            <View style={styles.container}>
                <Pressable onPress={() => navigation.goBack()}>
                    <Feather name={backIcon || 'chevron-left'} size={scale(25)} />
                </Pressable>

                <Label text={label} style={styles.label} />

                {showSearch ? (
                    <View style={styles.searchIconContainer}>
                        <Feather name={rightIcon || 'search'} size={scale(20)} color={appColors.white} />
                    </View>
                ) : (
                    <View />
                )}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: scale(20),
    },
    label: {
        fontWeight: '500',
        fontSize: scale(21),
    },
    searchIconContainer: {
        height: scale(45),
        width: scale(45),
        backgroundColor: appColors.primary,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: scale(25),
    },
});

export default ScreenHeader;
