import React, { Component } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { scale, verticalScale, moderateScale } from 'react-native-size-matters';

interface Props {}

interface State {}

export class Daily extends Component<Props, State> {
  render() {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Hello from Daily Component!</Text>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: moderateScale(20),
    backgroundColor: '#f0f0f0',
  },
  title: {
    fontSize: scale(24),
    color: '#333',
    marginBottom: verticalScale(10),
  },
});
