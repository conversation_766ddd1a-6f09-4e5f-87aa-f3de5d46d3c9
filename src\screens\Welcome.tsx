import { NavigationContainerProps } from '@react-navigation/native';
import React, { Component } from 'react';
import { StyleSheet, View, Text, ImageBackground, Image, Dimensions, Pressable } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {withTranslation, WithTranslation} from 'react-i18next';
import { appColors } from '../utils/appColors';
import { Ionicons } from '@expo/vector-icons';
import { scale } from 'react-native-size-matters';
import  Animated, { FadeInDown, FadeInLeft, FadeInRight }  from 'react-native-reanimated';
import { renderBold, renderRegular } from '../services/FontProvider';
import  i18n  from 'i18next';

interface Props extends WithTranslation {
    navigation : NavigationContainerProps;
}

interface State {}

class Welcome extends Component<Props, State> {


    changeLanguage = async(lng: string) => {
       await i18n.changeLanguage(lng);
    }
  
  render() {
    const {t} = this.props;
    return (      
        <ImageBackground 
          source={require('../images/background.jpg')} // Use the image source passed as a prop
          style={styles.backgroundImage}
          resizeMode="cover"
        >
            <View style={styles.container}>
                <LinearGradient colors={["transparent",'rgba(255, 255, 255, 0.9)', 'rgba(255, 255, 255, 1)']} style={styles.background}>
                    <View style={styles.wrapper}>
                        <Animated.Text style={styles.title} entering={FadeInRight.delay(300).duration(500)}>Report 1C</Animated.Text>
                        <Animated.Text style={styles.description} entering={FadeInRight.delay(500).duration(500)}>{t('welcome.ecommerce')} </Animated.Text>
                        <View style={styles.socialLoginWrapper}>
                            <Animated.View  entering={FadeInDown.delay(300).duration(700)}>
                                <Pressable style={styles.button} onPress={() => this.props.navigation.navigate("Register")}>
                                    <Ionicons name='mail-outline' size={20} color={appColors.primary}/>
                                    <Text style={styles.btnTxt}>{t('welcome.register')}</Text>
                                </Pressable>
                            </Animated.View>
                        </View>
                        <View style={styles.languageSelectionWrapper}>
                            <Animated.View  entering={FadeInLeft.delay(500).duration(700)}>
                              <Pressable onPress={() => this.changeLanguage('az')}>
                                  <Image
                                    source={require('../images/flag-az.png')}
                                    style={styles.userPhoto}
                                  />
                              </Pressable>
                            </Animated.View>
                            <Animated.View entering={FadeInRight.delay(500).duration(700)}>
                            <Pressable onPress={() => this.changeLanguage('ru')}>
                                <Image
                                  source={require('../images/flag-ru.png')}
                                  style={styles.userPhoto}
                                />
                            </Pressable>
                            </Animated.View>
                        </View>
                            <View style={styles.sameRow}>
                              <Text style={styles.loginTxt}>{t('welcome.haveAccount')} {" "}</Text>
                              <Pressable onPress={() => this.props.navigation.navigate("Login")}>
                                  <Text style={styles.loginTxtSpan}>{t('welcome.login')}</Text>
                              </Pressable>
                            </View>                      
                    </View>
                </LinearGradient>
            </View>
        </ImageBackground>
     
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background:{
    flex:1,
    position:'absolute',
    top:0,
    bottom:0,
    left:0,
    right:0,
    justifyContent:'flex-end'
  },
  backgroundImage: {
    flex: 1,
  },
  wrapper:{
    paddingBottom:scale(50),
    paddingHorizontal:scale(20),
    alignItems:'center'
  },
  title: {
    color: appColors.primary,
    fontSize: scale(27),
    fontFamily: "Jonova-Regular",
    fontWeight:'400',
    letterSpacing: 1.2,
    marginBottom: scale(5)
  },
  description: {
    color: appColors.gray,
    fontFamily:"Jonova-Regular",
    fontSize: scale(22),
    marginBottom: scale(20),
    letterSpacing: 1.2,
    lineHeight:30
  },
  socialLoginWrapper:{
    alignSelf:'stretch',
  },
  languageSelectionWrapper:{
    flexDirection:'row',
    justifyContent:'space-between',
  },
  userPhoto:{
    width:scale(75),
    height:scale(75),
    borderRadius:12.5,
  },
  button:{
    flexDirection:'row',
    padding: scale(10),
    borderColor:appColors.gray,
    borderRadius: scale(25),
    borderWidth:StyleSheet.hairlineWidth,
    alignItems:'center',
    justifyContent:'center',
    gap:5,
    marginBottom: scale(15),
  },
  btnTxt:{
    fontSize:scale(14),
    lineHeight:18,
    fontFamily:renderRegular(),
    color:appColors.black,
  },
  sameRow:{
    marginTop: scale(30),
    flexDirection:'row',
  },
  loginTxt:{    
    fontSize: scale(16),
    lineHeight:20,
    color: appColors.black,
    fontFamily: renderRegular(),
  },
  loginTxtSpan:{
    fontSize: scale(20),
    lineHeight:22,
    color:appColors.primary,
    fontFamily: renderBold(),
  }
});

export default withTranslation()(Welcome);
