import React, { Component } from 'react';
import { View, Text, StyleSheet, Image, Dimensions, TouchableOpacity } from 'react-native';
import { scale } from 'react-native-size-matters';
import { Product } from '../models/Product';
import { AntDesign, FontAwesome, FontAwesome5, Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { appColors } from '../utils/appColors';
import Animated, { FadeInDown } from 'react-native-reanimated';
import Label from './Label';
import WishlistService from '../services/WishlistService';

export interface ProductProps {
    item: Product;
    index: number;
    navigation: any;
    hotSale?: boolean;
    double?:boolean;
}

export class SearchItem extends Component<ProductProps> {
    handleNavigate = () => {
        const { navigation, item } = this.props;
        navigation.navigate("ProductDetails", { id : item.id });
    };

    addToWishlist = () => {
        const { item } = this.props;
        console.log(item.id);
        WishlistService.addProductToWishlist(1, item.id).then(() => {
            item.inWishlist = true;
        });
    };

    render() {
        const { item, index, hotSale, double } = this.props;

        return (
            <TouchableOpacity onPress={this.handleNavigate}>
                <Animated.View
                    style={double ? styles.containerDouble : styles.container}
                    entering={FadeInDown.delay(300 + index).duration(500)}
                >
                    <Image source={{ uri: item.images[0].possibleURL }} style={styles.productImage} />
                        {
                            item.rating === 0.0 || item.rating === 0
                            ?
                            null
                            :
                            <View style={styles.newLabel}>
                                <Ionicons name="star" color={appColors.primary} size={scale(16)} />
                                <Text style={styles.ratingTxt}>{item.rating}</Text>                         
                            </View>
                        }

                    {
                        item.inWishlist === false
                        ?
                        <TouchableOpacity style={styles.bookmarkBtn} onPress={this.addToWishlist}>
                            <Ionicons name="heart-outline" color={appColors.primary} size={scale(22)} />
                        </TouchableOpacity>
                        :
                        null
                    }

                    <View style={styles.priceContainer}>
                        <Text style={styles.title}>{item.name}</Text>
                        <Text style={styles.description}>{item.description}</Text>
                        <Text style={styles.priceTxt}>${item.price}</Text>
                        {
                            item.inCart
                                ?
                                <TouchableOpacity style={styles.badge}>
                                    <Text style={styles.priceTxt}>Proceed</Text>
                                    <MaterialCommunityIcons name="cart-arrow-right" color={appColors.white} size={scale(18)}/>
                                </TouchableOpacity>
                                :
                                <TouchableOpacity style={styles.badge}>
                                <Text style={styles.priceTxt}>${item.price}</Text>                                    
                                    <FontAwesome name="cart-arrow-down" color={appColors.white} size={scale(18)}/>
                                </TouchableOpacity>
                        }

                    </View>
                    
                </Animated.View>
            </TouchableOpacity>
        );
    }
}

const width = Dimensions.get('window').width - 20;

export default SearchItem;

const styles = StyleSheet.create({
    container: {
        width: width / 2 - scale(5),
        margin:scale(5),
        flexDirection:'row',
        gap:scale(5)
    },
    containerDouble: {
        width: width / 2 - scale(5),
    },
    productImage: {
        width: '100%',
        height: scale(150),
        borderRadius: scale(15),
        marginBottom: scale(10),
    },
    newLabel: {
        backgroundColor: 'rgba(255, 255, 255, 1)',
        position: 'absolute',
        top: scale(17),
        left: scale(5),
        padding: scale(3),
        borderRadius: scale(3),
        paddingHorizontal: scale(10),
        flexDirection:'row',
        
        gap:scale(4)
    },
    newLabelText: {
        fontSize: scale(10),
        color: appColors.white,
    },
    bookmarkBtn: {
        position: 'absolute',
        right: scale(5),
        top: scale(15),
        backgroundColor: 'rgba(255, 255, 255, 1)',
        padding: scale(5),
        borderRadius: scale(30),
    },
    title: {
        fontSize: scale(14),
        fontWeight: 'bold',
        color: appColors.black,
        letterSpacing: 1.1,
    },
    description:{
        fontSize: scale(12),
        color: appColors.black,
        letterSpacing: 1.1,
    },
    priceContainer: {
        flexDirection: 'column',
        justifyContent: 'space-around',
        marginBottom: scale(8),
    },
    priceTxt: {
        fontSize: scale(14),
        fontWeight: '700',
        color: appColors.white,
    },
    ratingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 5,
    },
    ratingTxt: {
        fontSize: scale(15),
        color: appColors.black,
        textAlign:'center',
    },
    badge: {
        backgroundColor: appColors.primary,
        flexDirection: 'row',
        padding: scale(10),
        justifyContent: 'space-between',
        gap: scale(5),
        alignItems: 'center',
        borderRadius:  scale(3),
        width:'75%'
    },
});
